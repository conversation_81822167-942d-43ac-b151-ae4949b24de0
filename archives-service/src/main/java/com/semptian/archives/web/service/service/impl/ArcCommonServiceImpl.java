package com.semptian.archives.web.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.semptian.archives.web.core.common.constent.CommonConstent;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.enums.ConstantEnum;
import com.semptian.archives.web.core.common.enums.DataTypeEnum;
import com.semptian.archives.web.core.common.util.ArcIdUtil;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.core.common.util.PageHelpUtil;
import com.semptian.archives.web.dao.archive.entity.*;
import com.semptian.archives.web.dao.archive.mapper.ArcDrillDownFieldConfigMapper;
import com.semptian.archives.web.dao.archive.mapper.MySqlMapper;
import com.semptian.archives.web.service.common.config.EsResourceNameConfig;
import com.semptian.archives.web.service.common.enums.*;
import com.semptian.archives.web.service.common.util.ArcCommonUtils;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.common.util.StringTemplateUtil;
import com.semptian.archives.web.service.fegin.BasicFeign;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.*;
import com.semptian.base.builders.QueryBuilder;
import com.semptian.base.builders.QueryBuilders;
import com.semptian.base.enums.ReturnCode;
import com.semptian.base.model.TianhePageModel;
import com.semptian.base.service.ReturnModel;
import com.semptian.base.template.TianHeDorisTemplate;
import com.semptian.redis.template.RedisOps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.convert.EntityWriter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.semptian.archives.web.core.common.constent.CommonConstent.ES_ARC_ID_FIELD;

@Service
@Slf4j
public class ArcCommonServiceImpl implements ArcCommonService {

    @Resource
    private MySqlMapper mySqlMapper;

    @Resource
    private ComposeQueryService composeQueryService;

    @Resource
    ArcCommonService arcCommonService;

    @Resource
    private RedisOps redisOps;

    @Value("${spring.redis.cache-prefix}")
    String redisKeyPrefix;

    @Value("${arc.total.info.expire.time:3:00}")
    String arcTotalInfoExpireTime;

    public static boolean isOpen = true;

    public static final String UPDATE_ARC_NAME_KEY = "update_arc_name:";

    @Value("${archive.search.timeOut:10000}")
    private int timeOut;

    @Value("${archive.search.docNum:50000000}")
    private int docNum;

    @Value("${archive.dbName.dwd:dwd}")
    private String dwdDbName;

    @Value("${archive.dbName.dws:dws}")
    private String dwsDbName;

    @Value("${archive.playUrl.voip:}")
    private String voicePlayUrl;

    @Value("${archive.playUrl.fax:}")
    private String faxViewUrl;

    @Value("${archive.playUrl.base}")
    private String playUrlBase;

    @Value("${archive.tag.top.dateOpt:1}")
    private Integer tagTopDateOpt;

    @Value("${archive.tag.top.num:10}")
    private Integer tagTopDateNum;

    @Lazy
    @Resource
    private BasicFeign basicFeign;

    @Resource
    private ArcCustomColumnsService customColumnsService;

    /**
     * 全息档案缓存开关配置
     *
     * @param isOpen
     */
    @Value("${archive.redis.switch:true}")
    public void setIsOpen(boolean isOpen) {
        ArcCommonServiceImpl.isOpen = isOpen;
    }

    @Resource
    private EsQueryService esQueryService;

    @Resource
    private EsResourceNameConfig esResourceNameConfig;

    @Resource
    private EsDataOperateService esDataOperateService;

    private static final List<String> esResourceNameList = new ArrayList<>(10);

    @Resource
    ArcDrillDownFieldConfigMapper arcDrillDownFieldConfigMapper;

    @Resource
    private TianHeDorisTemplate tianHeDorisTemplate;

    @Resource
    ArcDrillDownFieldTranslateConfigService arcDrillDownFieldTranslateConfigService;

    @Resource
    UserRelationDataService userRelationDataService;

    @Override
    public List<ArcEsEntity> getArcInfoByIds(List<String> arcIdList, ArcTypeEnum arcTypeEnum) {
        String resourceName = getEsResourceNameByArcType(arcTypeEnum);
        List<Map<String, Object>> records = esQueryService.getRecordsByIds("id", arcIdList, resourceName);

        return records.stream().map(record -> BeanUtil.toBean(record, ArcEsEntity.class)).collect(Collectors.toList());
    }

    @Override
    public List<ArcEsEntity> getArcInfoByIds(List<String> arcIdList) {
        List<String> resourceNameList = getAllEsResourceName();
        List<Map<String, Object>> records = esQueryService.getRecordsByIds("id", arcIdList, resourceNameList.toArray(new String[0]));

        return records.stream().map(record -> BeanUtil.toBean(record, ArcEsEntity.class)).collect(Collectors.toList());
    }

    @Override
    public ArcEsEntity getArcInfoById(String arcId, Integer arcType) {
        List<ArcEsEntity> arcInfoByIds = getArcInfoByIds(Lists.newArrayList(arcId));
        if (CollUtil.isNotEmpty(arcInfoByIds)) {
            return arcInfoByIds.get(0);
        }
        return null;
    }

    @Override
    public ArcEsEntity getArcInfoByArcAccount(String arcAccount, Integer arcType, String arcAccountType) {
        QueryBuilder queryBuilder = QueryBuilders.termQuery(ArcQueryEsConditionEnum.ARCHIVE_NAME.getFieldName(), arcAccount);

        Map<String, Object> queryResult = this.query(0, 1, queryBuilder, null, arcType, Lists.newArrayList(arcType));
        List<ArcEsEntity> arcInfoList = (List) queryResult.get("data");

        if (CollUtil.isNotEmpty(arcInfoList)) {
            return arcInfoList.get(0);
        }
        return null;
    }

    @Override
    @SuppressWarnings({"rawtypes", "unchecked"})
    public Map<String, Object> query(Integer onPage, Integer size, QueryBuilder queryBuilder, Map<String, String> sortFields, Integer archivesType, List<Integer> permmisionList) {
        Map<String, Object> result = new HashMap<>(4);

        List<String> indexList = getEsResourceNameByArcTypeAndPermission(archivesType.toString(), permmisionList);

        TianhePageModel tianhePageModel = esQueryService.query(onPage, size, queryBuilder, sortFields, timeOut, docNum, indexList.toArray(new String[0]));

        List<Map<String, Object>> records = tianhePageModel.getRecords();

        if (records == null || tianhePageModel.getTotal() == 0) {
            records = new ArrayList<>();
        }
        List<ArcEsEntity> arcInfoList = records.stream().map(record -> BeanUtil.toBean(record, ArcEsEntity.class)).collect(Collectors.toList());

        result.put("data", arcInfoList);
        result.put("total", tianhePageModel.getTotal());
        return result;
    }

    /**
     * @param onPage
     * @param size
     * @param queryBuilder
     * @param sortFields
     * @param archivesTypes  多个以逗号分隔 1,2
     * @param permmisionList
     * @return
     */
    public Map<String, Object> query(Integer onPage, Integer size, QueryBuilder queryBuilder, Map<String, String> sortFields, String archivesTypes, List<Integer> permmisionList) {
        Map<String, Object> result = new HashMap<>(4);

        List<String> indexList = getEsResourceNameByArcTypeAndPermission(archivesTypes, permmisionList);

        TianhePageModel tianhePageModel = esQueryService.query(onPage, size, queryBuilder, sortFields, timeOut, docNum, indexList.toArray(new String[0]));

        List<Map<String, Object>> records = tianhePageModel.getRecords();

        if (records == null || tianhePageModel.getTotal() == 0) {
            records = new ArrayList<>();
        }
        List<ArcEsEntity> arcInfoList = records.stream().map(record -> BeanUtil.toBean(record, ArcEsEntity.class)).collect(Collectors.toList());

        result.put("data", arcInfoList);
        result.put("total", tianhePageModel.getTotal());
        return result;
    }

    @Override
    public List<Map<String, Object>> getCommonServiceListResult(String serviceCode, Map<String, Object> params) {
        return getCommonServiceListResult(serviceCode, params, false, null, null, false, "");
    }

    @Override
    public List<Map<String, Object>> getCommonServiceListResult(String serviceCode, Map<String, Object> params, PageWarpEntity pageWarpEntity) {
        return getCommonServiceListResult(serviceCode, params, true, pageWarpEntity.getOnPage(), pageWarpEntity.getSize(), false, pageWarpEntity.getSortCondition());
    }

    @Override
    public PageResultModel getCommonServicePageResult(String serviceCode, Map<String, Object> params, PageWarpEntity pageWarpEntity) {
        PageResultModel pageResultModel = new PageResultModel();

        //先进行总条数查询，如果条数为0，则直接返回结果否则继续查询返回结果
        Long total = getCommonServiceCountResult(serviceCode, params, true);

        if (ObjectUtil.isNull(total) || total == 0) {
            pageResultModel.setTotal(0L);
            pageResultModel.setList(Lists.newArrayList());
            return pageResultModel;
        }

        List<Map<String, Object>> result = getCommonServiceListResult(serviceCode, params, true, pageWarpEntity.getOnPage(), pageWarpEntity.getSize(), false, pageWarpEntity.getSortCondition());

        pageResultModel.setTotal(total);
        pageResultModel.setList(result);
        return pageResultModel;
    }

    @Override
    public List<Map<String, Object>> getCommonServiceListResult(String serviceCode, Map<String, Object> params, Boolean needSqlEscape) {
        return getCommonServiceListResult(serviceCode, params, false, null, null, needSqlEscape, "");
    }

    @Override
    public List<Map<String, Object>> getCommonServiceListResult(String serviceCode, Map<String, Object> params, Boolean needWarpPageSql, Integer onPage, Integer size, String sortCondition) {
        return getCommonServiceListResult(serviceCode, params, false, null, null, false, sortCondition);
    }

    @Override
    public List<Map<String, Object>> getCommonServiceListResult(String serviceCode, Map<String, Object> params, Boolean needWarpPageSql, Integer onPage, Integer size) {
        return getCommonServiceListResult(serviceCode, params, needWarpPageSql, onPage, size, false, "");
    }

    @Override
    public List<Map<String, Object>> getCommonServiceListResult(String serviceCode, Map<String, Object> params, Boolean needWarpPageSql, Integer onPage, Integer size, Boolean needSqlEscape, String sortCondition) {
        //查询code对应的SQL
        Map<String, String> serviceSQL = mySqlMapper.getSQlByCode(serviceCode);
        String ckSQL = serviceSQL.get("ckSQL");

        if (needWarpPageSql) {
            if (StringUtils.isNotBlank(sortCondition)) {
                ckSQL = PageHelpUtil.getPageSql(PageHelpUtil.getSortSql(ckSQL, sortCondition), onPage, size);
            } else {
                ckSQL = PageHelpUtil.getPageSql(ckSQL, onPage, size);
            }
        } else {
            if (StringUtils.isNotBlank(sortCondition)) {
                ckSQL = PageHelpUtil.getSortSql(ckSQL, sortCondition);
            }
        }

        return composeQueryService.queryForList(ckSQL, params, serviceCode, needSqlEscape);
    }

    @Override
    public Long getCommonServiceCountResult(String serviceCode, Map<String, Object> params) {
        return getCommonServiceCountResult(serviceCode, params, true);
    }

    @Override
    public Double getCommonServiceSumResult(String serviceCode, Map<String, Object> params) {
        //查询code对应的SQL
        Map<String, String> serviceSQL = mySqlMapper.getSQlByCode(serviceCode);
        String ckSQL = serviceSQL.get("ckSQL");
        return composeQueryService.queryForSum(ckSQL, params, serviceCode);
    }

    @Override
    public Long getCommonServiceCountResult(String serviceCode, Map<String, Object> params, Boolean needWarpPageSql) {
        return getCommonServiceCountResult(serviceCode, params, needWarpPageSql, false);
    }

    /**
     * @param serviceCode
     * @param params
     * @param needWarpPageSql 是否需要将原始 sql 包装成 count sql
     * @return
     */
    @Override
    public Long getCommonServiceCountResult(String serviceCode, Map<String, Object> params, Boolean needWarpPageSql, Boolean needSqlEscape) {
        //查询code对应的SQL
        Map<String, String> serviceSQL = mySqlMapper.getSQlByCode(serviceCode);
        String ckSQL = serviceSQL.get("ckSQL");

        if (needWarpPageSql) {
            if (StringUtils.isNotBlank(ckSQL)) {
                ckSQL = PageHelpUtil.getCountSql(ckSQL);
            }
        }

        return composeQueryService.queryForCount(ckSQL, params, serviceCode, needSqlEscape);
    }

    @Override
    public String getRedisKey(String arcId, String methodName, Object... objects) {
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(arcId)) {
            stringBuilder.append(arcId).append(methodName);
        } else {
            stringBuilder.append(methodName);
        }

        Long startHourTime = DateUtils.getCurrentHourStart(new Date());
        stringBuilder.append(startHourTime);
        if (objects != null) {
            for (Object obj : objects) {
                stringBuilder.append(obj);
            }
        }
        String redisKey = SecureUtil.md5(stringBuilder.toString());
        return redisKeyPrefix + redisKey;
    }

    @Override
    public String getRedisKeyByDay(String arcId, String methodName, Object... objects) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(arcId).append(methodName);
        Date date = new Date();
        Long currentTime = date.getTime();
        Long endDayTime = DateUtils.getOneDayEnd(date);
        Long currentDayStart = DateUtils.getCurrentDayStart(date);
        if (currentTime >= currentDayStart) {
            stringBuilder.append(endDayTime);
            if (objects != null) {
                for (Object obj : objects) {
                    stringBuilder.append(obj);
                }
            }
            String redisKey = SecureUtil.md5(stringBuilder.toString());
            return redisKeyPrefix + redisKey;
        } else {
            //如果缓存时间小于当天5点钟，整点缓存1小时
            stringBuilder.append(DateUtils.getCurrentHourStart(date));
            if (objects != null) {
                for (Object obj : objects) {
                    stringBuilder.append(obj);
                }
            }
            String redisKey = SecureUtil.md5(stringBuilder.toString());
            return redisKeyPrefix + redisKey;
        }
    }

    @Override
    public String getRedisKeys(String methodName, Object... objects) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(methodName);
        if (objects != null) {
            for (Object obj : objects) {
                stringBuilder.append(obj);
            }
        }
        String redisKey = SecureUtil.md5(stringBuilder.toString());
        return redisKeyPrefix + redisKey;
    }

    @Override
    public String getRedisKeyByIndexCount(Boolean containToday, Object... objects) {
        StringBuilder stringBuilder = new StringBuilder();
        String redisKey = ConstantEnum.ALL_ARCHIVES.getValue();
        if (containToday) {
            redisKey = redisKey + ConstantEnum.CONTAIN_TODAY.getValue();
        }
        if (objects != null) {
            for (Object obj : objects) {
                stringBuilder.append(obj);
            }
        }
        redisKey = redisKey + SecureUtil.md5(stringBuilder.toString());
        return redisKeyPrefix + redisKey;
    }

    @Override
    public List<String> getEsResourceNameByArcTypeAndPermission(String arcType, List<Integer> permmisonList) {
        List<String> indexList = new ArrayList<>();
        if ("0".equals(arcType)) {
            for (Integer permission : permmisonList) {
                ArcTypeEnum arcTypeEnum = ArcTypeEnum.getArcTypeByKey(permission.toString());
                String esResourceNameByArcType = getEsResourceNameByArcType(Objects.requireNonNull(arcTypeEnum));
                indexList.add(esResourceNameByArcType);
            }
        } else {
            String[] arcTypeArray = arcType.split(",");

            for (String arcTypeStr : arcTypeArray) {
                if (permmisonList.contains(Integer.valueOf(arcTypeStr))) {
                    ArcTypeEnum arcTypeEnum = ArcTypeEnum.getArcTypeByKey(arcTypeStr);
                    String esResourceNameByArcType = getEsResourceNameByArcType(Objects.requireNonNull(arcTypeEnum));
                    indexList.add(esResourceNameByArcType);
                }
            }
        }
        return indexList;
    }

    private String getEsResourceNameByArcType(ArcTypeEnum arcTypeEnum) {
        String resourceName = "";
        switch (arcTypeEnum) {
            case FIXED_IP:
                resourceName = esResourceNameConfig.getFixIp();
                break;
            case APP:
                resourceName = esResourceNameConfig.getApp();
                break;
            case WEB_SITE:
                resourceName = esResourceNameConfig.getWebsite();
                break;
            case PHONE:
                resourceName = esResourceNameConfig.getPhone();
                break;
            case RADIUS:
                resourceName = esResourceNameConfig.getRadius();
                break;
            case EMAIL:
                resourceName = esResourceNameConfig.getEmail();
                break;
            case IM:
                resourceName = esResourceNameConfig.getIm();
                break;
            case NET_PROTOCOL:
                resourceName = esResourceNameConfig.getProtocol();
                break;
            default:
                break;
        }
        return resourceName;
    }

    private List<String> getAllEsResourceName() {
        if (CollUtil.isEmpty(esResourceNameList)) {
            esResourceNameList.add(esResourceNameConfig.getWebsite());
            esResourceNameList.add(esResourceNameConfig.getApp());
            esResourceNameList.add(esResourceNameConfig.getFixIp());
            esResourceNameList.add(esResourceNameConfig.getPhone());
            esResourceNameList.add(esResourceNameConfig.getRadius());
            esResourceNameList.add(esResourceNameConfig.getEmail());
            esResourceNameList.add(esResourceNameConfig.getIm());
            esResourceNameList.add(esResourceNameConfig.getProtocol());
        }
        return esResourceNameList;
    }

    @Override
    public List<Map<String, Object>> getHeaders(Integer dataType, Integer isAll, DrillDownSceneEnum drillDownSceneEnum, String lang, boolean isDisplay) {
        return getHeaders(dataType, isAll, drillDownSceneEnum, lang, isDisplay, 0, null);
    }

    @Override
    public List<Map<String, Object>> getHeaders(Integer dataType, Integer isAll, DrillDownSceneEnum drillDownSceneEnum, String lang,
                                                boolean isDisplay, Integer isCustomField, String userId) {
        //如果dataType为空，不返回数据
        if (dataType == null || dataType == 0) {
            return new ArrayList<>();
        }

        if (isCustomField != null && isCustomField == 1
                && StringUtils.isNotBlank(userId)) {
            List<Map<String, Object>> columnsList = getCustomFieldList(dataType, drillDownSceneEnum, lang, userId);
            if (columnsList != null) return columnsList;
        }

        List<Map<String, Object>> list = new ArrayList<>();

        if (DrillDownSceneEnum.AUTHENTICATION_BILLING == drillDownSceneEnum || StringUtils.isNotEmpty(DrillDownNormDataTypeEnum.getByKey(dataType))) {
            String dataTypeInConfig = getDataTypeInConfig(dataType, drillDownSceneEnum);
            //从明细下钻展示列配置表获取字段类型
            QueryWrapper<ArcDrillDownFieldConfigEntity> queryWrapper = new QueryWrapper<ArcDrillDownFieldConfigEntity>().eq("data_type", dataTypeInConfig).eq("is_hide", 0);
            //根据展示字段类型获取对应字段
            if (DrillDownSceneEnum.ATTACH_SOURCE_DETAIL.equals(drillDownSceneEnum) || DrillDownSceneEnum.PHONE_EXTRACT_SOURCE_DETAIL.equals(drillDownSceneEnum)) {
                queryWrapper.eq("is_attach_source", 1);
            } else if (isAll == 0) {
                queryWrapper.eq("is_core", 1);
            }
            arcDrillDownFieldConfigMapper.selectList(queryWrapper).forEach(entity -> {
                //如果字段中文描述为空，则不展示
                if (LanguageEnum.ZH_CN.getLang().equals(lang) && StringUtils.isEmpty(entity.getDescZh())) {
                    return;
                }
                Map<String, Object> map = new HashMap<>();
                if (LanguageEnum.FR_DZ.getLang().equals(lang)) {
                    map.put("title", entity.getDescFr());
                } else if (LanguageEnum.EN_US.getLang().equals(lang)) {
                    map.put("title", entity.getDescEn());
                } else {
                    map.put("title", entity.getDescZh());
                }

                //如果字段名称为position，需要特殊加引号处理，否则DAT侧会拦截sql
                if ("position".equals(entity.getField())) {
                    map.put("field", "_position");
                } else {
                    map.put("field", entity.getField());
                }

                if (entity.getSortable() == 1) {
                    map.put("sortable", "custom");
                } else {
                    map.put("sortable", false);
                }

                map.put("searchable", entity.getIsSearch() == 1);
                //如果sort不在100-500之间，则不展示
                int sort = entity.getSort();
                if (sort < 100 || sort > 500) {
                    return;
                }
                map.put("sort", sort);

                // isCore isShow 是和列定制相关的
                map.put("isCore", entity.getIsCore());
                if (isCustomField != null && isCustomField == 1) {
                    // 列定制时, 针对非核心字段进行特殊处理
                    if (entity.getIsCore() != null && entity.getIsCore() == 1) {
                        map.put("isShow", 1);
                    } else {
                        map.put("isShow", 0);
                    }
                } else {
                    map.put("isShow", 1);
                }

                list.add(map);
            });
            //对list按照sort进行从小到大排序
            list.sort(Comparator.comparingInt(o -> (int) o.get("sort")));
        }
        //附件相关字段特殊处理
        if (isDisplay) {
            list.removeIf(header -> "attach_download_path".equals(header.get("field")) || "attach_names".equals(header.get("field")) || "attach_sizes".equals(header.get("field")) || "attach_md5s".equals(header.get("field")) || "attach_text".equals(header.get("field")));
        }
        //删除语种字段表头
        list.removeIf(header -> "sms_lang".equals(header.get("field")) || "lang_type".equals(header.get("field")));
        return list;
    }

    private List<Map<String, Object>> getCustomFieldList(Integer dataType, DrillDownSceneEnum drillDownSceneEnum, String lang, String userId) {
        try {
            // 直接返回列定制
            QueryWrapper<ArcCustomColumnsEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("user_id", userId);
            wrapper.eq("data_type", dataType);
            ArcCustomColumnsEntity one = customColumnsService.getOne(wrapper);

            String dataTypeInConfig = getDataTypeInConfig(dataType, drillDownSceneEnum);
            QueryWrapper<ArcDrillDownFieldConfigEntity> queryWrapper = new QueryWrapper<ArcDrillDownFieldConfigEntity>().eq("data_type", dataTypeInConfig).eq("is_hide", 0);
            List<ArcDrillDownFieldConfigEntity> arcDrillDownFieldConfigEntities = arcDrillDownFieldConfigMapper.selectList(queryWrapper);
            HashMap<String, ArcDrillDownFieldConfigEntity> fieldMap = new HashMap<>();
            for (ArcDrillDownFieldConfigEntity arcDrillDownFieldConfigEntity : arcDrillDownFieldConfigEntities) {
                fieldMap.put(arcDrillDownFieldConfigEntity.getField(), arcDrillDownFieldConfigEntity);
            }


            if (one != null) {
                String columns = one.getColumns();
                JSONArray columnsArr = JSONObject.parseArray(columns);
                // columnsArr 转换成 list
                List<Map<String, Object>> columnsList = columnsArr.stream().map(item -> {
                    JSONObject itemObj = (JSONObject) item;
                    String fileString = itemObj.getString("field").toString();
                    ArcDrillDownFieldConfigEntity arcDrillDownFieldConfigEntity = fieldMap.get(fileString);

                    if (arcDrillDownFieldConfigEntity != null) {
                        if (LanguageEnum.FR_DZ.getLang().equals(lang)) {
                            itemObj.put("title", arcDrillDownFieldConfigEntity.getDescFr());
                        } else if (LanguageEnum.EN_US.getLang().equals(lang)) {
                            itemObj.put("title", arcDrillDownFieldConfigEntity.getDescEn());
                        } else {
                            itemObj.put("title", arcDrillDownFieldConfigEntity.getDescZh());
                        }
                    }

                    Map<String, Object> map = new HashMap<>();
                    map.putAll(itemObj);
                    return map;
                }).collect(Collectors.toList());

                return columnsList;
            }
        } catch (Exception e) {
            log.error("get custom header error, userId [{}], dataType [{}] ", userId, dataType, e);
        }
        return null;
    }

    private static String getDataTypeInConfig(Integer dataType, DrillDownSceneEnum drillDownSceneEnum) {
        String dataTypeInConfig;
        //如果是NF_VPN_URL及NF_VPN_OTHER_LOG协议，需要对data_type进行转换
        if (DrillDownNormDataTypeEnum.NF_VPN_OTHER_LOG.getKey() == dataType) {
            dataType = DrillDownNormDataTypeEnum.NF_OTHER_LOG.getKey();
        } else if (DrillDownNormDataTypeEnum.NF_VPN_URL.getKey() == dataType) {
            dataType = DrillDownNormDataTypeEnum.NF_URL.getKey();
        }
        //如果是鉴权计费，1,2(移动网radius),3(location),99(全部展示)
        if (DrillDownSceneEnum.AUTHENTICATION_BILLING == drillDownSceneEnum) {
            if (dataType == 1 || dataType == 2) {
                dataTypeInConfig = "mobile_radius";
            } else if (dataType == 3) {
                dataTypeInConfig = "mobile_location";
            } else {
                dataTypeInConfig = "mobile_radius";
            }
        } else {
            //计算表中data_type
            dataTypeInConfig = DrillDownNormDataTypeEnum.getByKey(dataType);
        }
        return dataTypeInConfig;
    }

    public List<Map<String, Object>> getSearchHeaders(Integer dataType) {
        //如果是NF_VPN_URL及NF_VPN_OTHER_LOG协议，需要对data_type进行转换
        if (DrillDownNormDataTypeEnum.NF_VPN_OTHER_LOG.getKey() == dataType) {
            dataType = DrillDownNormDataTypeEnum.NF_OTHER_LOG.getKey();
        } else if (DrillDownNormDataTypeEnum.NF_VPN_URL.getKey() == dataType) {
            dataType = DrillDownNormDataTypeEnum.NF_URL.getKey();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(DrillDownNormDataTypeEnum.getByKey(dataType))) {
            //从明细下钻展示列配置表获取字段类型
            arcDrillDownFieldConfigMapper.selectList(new QueryWrapper<ArcDrillDownFieldConfigEntity>().eq("data_type", DrillDownNormDataTypeEnum.getByKey(dataType)).eq("is_search", 1)).forEach(entity -> {
                Map<String, Object> map = new HashMap<>();
                if ("position".equals(entity.getField())) {
                    map.put("field", "`position`");
                } else {
                    map.put("field", entity.getField());
                }
                list.add(map);
            });
        }
        return list;
    }

    @Override
    public DrillDownResultEntity getDrillDownDataDetail(DrillDownModel drillDownModel, String userId) {
        log.info("getDrillDownDataDetail, param is: [{}]", JSONObject.toJSON(drillDownModel).toString());
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(new DateModel(drillDownModel.getStartDay(), drillDownModel.getEndDay(), drillDownModel.getDateOption()));
        DateUtils.validateParamMap(paramMap, drillDownModel.getCreateDay());
        drillDownModel.setStartDay(paramMap.getStartDay());
        drillDownModel.setEndDay(paramMap.getEndDay());
        //根据条件生产对应的doris查询明细sql
        String sql = getDataDetailSql(drillDownModel, userId);

        if (StringUtils.isNotBlank(sql)) {
            String pageSql = PageHelpUtil.getPageSql(sql, drillDownModel.getOnPage(), drillDownModel.getSize());
            String countSql = PageHelpUtil.getCountSql(sql);
            List<Map<String, Object>> result = composeQueryService.queryForList(pageSql, null, "GetDrillDownDataDetail");
            Long total = composeQueryService.queryForCount(countSql, null, "GetDrillDownDataDetailTotal");
            // 后置处理逻辑
            List<Map<String, Object>> processedResult = afterProcess(result, drillDownModel);
            return DrillDownResultEntity.builder().list(processedResult).total(total).build();
        }

        return DrillDownResultEntity.builder().list(new ArrayList<>()).total(0L).build();
    }

    @Override
    public DrillDownResultEntity getDrillDownDataDetail(DrillDownModel drillDownModel) {
        return getDrillDownDataDetail(drillDownModel, null);
    }

    /**
     * 返回结果后置处理
     *
     * @param result
     * @param drillDownModel
     * @return
     */
    private List<Map<String, Object>> afterProcess(List<Map<String, Object>> result, DrillDownModel drillDownModel) {
        // 根据 下钻条件, 可能会针对特定协议进行返回结果集特殊处理, 如字典值翻译
        //字段值翻译
        result = arcDrillDownFieldTranslateConfigService.fieldTranslate(result, drillDownModel.getLang(), drillDownModel.getDataType());

        if (String.valueOf(DrillDownNormDataTypeEnum.CALL.getKey()).equals(drillDownModel.getDataType().toString()) ||
                String.valueOf(DrillDownNormDataTypeEnum.FAX.getKey()).equals(drillDownModel.getDataType().toString())
        ) {
            buildPlayUrl(result, drillDownModel);
        }

        //附件字段特殊处理
        result.forEach(map -> {
            if (map.containsKey("attach_num") && map.containsKey("attach_download_path") && map.containsKey("attach_md5s")
                    && map.containsKey("attach_names") && map.containsKey("attach_sizes") ) {
                List<Map<String, String>> attachMapList = Lists.newArrayList();

                try {
                    JSONArray attachNames = JSON.parseArray(map.getOrDefault("attach_names", "[]").toString());
                    JSONArray attachSizes = JSON.parseArray(map.getOrDefault("attach_sizes", "[]").toString());
                    JSONArray downloadPaths = JSON.parseArray(map.getOrDefault("attach_download_path", "[]").toString());

                    if (downloadPaths != null && !downloadPaths.isEmpty() && StringUtils.isNotEmpty(map.getOrDefault("attach_names", "").toString()) && downloadPaths.size() == attachNames.size() && downloadPaths.size() == attachSizes.size()) {
                        for (int i = 0; i < downloadPaths.size(); i++) {
                            Map<String, String> attachMap = new HashMap<>();
                            attachMap.put("attach_download_path", downloadPaths.get(i).toString().trim());
                            attachMap.put("attach_name", attachNames.get(i).toString().trim());
                            attachMap.put("attach_size", attachSizes.get(i).toString().trim());
                            attachMapList.add(attachMap);
                        }
                    }
                } catch (Exception e) {
                    log.error("afterProcess is fail;error is {} for data: {}",e.getMessage(), map);
                }

                map.put("attach_num", attachMapList);
                map.remove("attach_download_path");
                map.remove("attach_md5s");
                map.remove("attach_names");
                map.remove("attach_sizes");
                map.remove("attach_text");
            }
        });
        return result;
    }

    private void buildPlayUrl(List<Map<String, Object>> result, DrillDownModel drillDownModel) {
        try {
            // 号码档案扩线需要补全 playUrl 用于语音播放
            if (CollectionUtil.isNotEmpty(result)) {
                for (Map<String, Object> data : result) {
                    //Long startTime = DateUtil.parse(String.valueOf(data.get("start_time")), DateUtils.TIMESTAMP_TO_DATETIME_FORMAT).getTime();
                    //Long endTime = DateUtil.parse(String.valueOf(data.get("end_time")), DateUtils.TIMESTAMP_TO_DATETIME_FORMAT).getTime();

                    if (String.valueOf(DrillDownNormDataTypeEnum.FAX.getKey()).equals(drillDownModel.getDataType().toString())) {
                        // call 传真下钻
                        //String playUrl = getPlayUrl(String.valueOf(data.get("call_id")), null, String.valueOf(data.get("calling_number")), String.valueOf(data.get("calling_number")),
                        //      startTime, endTime, Long.valueOf(String.valueOf(data.get("duration"))));
                        String playUrl = getPlayUrlFax(String.valueOf(data.get("file_path")));
                        data.put("playUrl", playUrl);
                    } else if (String.valueOf(DrillDownNormDataTypeEnum.CALL.getKey()).equals(drillDownModel.getDataType().toString())) {
                        // call 语音下钻下钻
                        //String playUrl = getPlayUrl(null, String.valueOf(data.get("file_path")), String.valueOf(data.get("calling_number")), String.valueOf(data.get("called_number")),
                        //      startTime, endTime, Long.valueOf(String.valueOf(data.get("duration"))));
                        String playUrl = getPlayUrl(String.valueOf(data.get("file_path")));
                        data.put("playUrl", playUrl);
                    }
                }
            }
        } catch (Exception e) {
            log.error("build playUrl error ", e);
        }
    }


    private String getPlayUrl(String voicePath) {
        if (StringUtils.isNotBlank(voicePath)) {
            //语音
            return voicePath;
        } else {
            return "";
        }
    }

    private String getPlayUrlFax(String filePath) {
        if (StringUtils.isNotBlank(filePath)) {
            //传真
            if (filePath.contains(",")) {//如果是多个地址
                String[] filePathSplit = filePath.split(",");
                StringBuilder sb = new StringBuilder();
                for (String s : filePathSplit) {//遍历单个地址
                    if (s.length() > 2) {//如果长度大于2
                        String subString = s.substring(0, 2);
                        if (playUrlBase.contains(subString)) {//如果在playUrlBase中，返回地址
                            sb.append(faxViewUrl);
                            sb.append(s.substring(2)).append(",");
                        } else {//如果不在playUrlBase中，返回无效值
                            return "";
                        }
                    } else {//如果长度小于等于2，返回无效值
                        return "";
                    }
                }
                return sb.substring(0, sb.length() - 1);
            } else {//如果是单个地址
                if (filePath.length() > 2) {//如果长度大于2
                    String subString = filePath.substring(0, 2);
                    if (playUrlBase.contains(subString)) {//如果在playUrlBase中，返回地址
                        return faxViewUrl + filePath.substring(2);
                    } else {//如果不在playUrlBase中，返回无效值
                        return "";
                    }
                } else {//如果长度小于等于2，返回无效值
                    return "";
                }
            }
        } else {//如果为空，返回无效值
            return "";
        }
    }

    /**
     * 根据条件生产对应的doris查询明细sql
     *
     * @param drillDownModel
     * @return
     */
    public String getDataDetailSql(DrillDownModel drillDownModel, String userId) {
        if (drillDownModel.getDataType() == null || drillDownModel.getDataType() == 0) {
            return null;
        }
        Integer isCustomField = 0;
        if (drillDownModel.getDrillDownSceneEnum().equals(DrillDownSceneEnum.WEBSITE_ARC_VISITOR_DETAIL) ||
                drillDownModel.getDrillDownSceneEnum().equals(DrillDownSceneEnum.APP_ARC_VISITOR_DETAIL)) {
            isCustomField = 1;
        }

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT ");

        //首先根据下钻数据类型读取对应的字段配置
        List<Map<String, Object>> list = getHeaders(drillDownModel.getDataType(), drillDownModel.getIsAll(), drillDownModel.getDrillDownSceneEnum(), drillDownModel.getLang(), false, isCustomField, userId);
        // 用于搜素的字段信息
        List<Map<String, Object>> searchHeaders = getSearchHeaders(drillDownModel.getDataType());
        appendFiledCondition(list, sb);
        sb.append(" FROM ");
        sb.append(dwdDbName).append(".").append(DrillDownNormDataTypeEnum.getTableNameByKey(drillDownModel.getDataType()));
        appendTimeCondition(drillDownModel, sb);
        appendKeywordCondition(drillDownModel, searchHeaders, sb);

        //根据dataType条件生成where条件，注意如果是公共表才需要加入次条件过滤
        if (null != drillDownModel.getDataType() && DrillDownNormDataTypeEnum.getTableNameByKey(drillDownModel.getDataType()).contains("_others")) {
            sb.append(String.format(" AND norm_data_type in (%s)", drillDownModel.getDataType()));
        }

        appendNetActionCondition(drillDownModel, sb);

        // ======== 上面是通用化的下钻条件，下面期望根据 下钻场景 及协议类型，进行特殊化处理 ========//
        appendSqlByScene(drillDownModel, sb);

        appendSortCondition(drillDownModel, sb);

        return sb.toString();
    }

    private static void appendSortCondition(DrillDownModel drillDownModel, StringBuilder sb) {
        //根据传入排序方式进行排序，默认时间倒排
        if (StringUtils.isNotEmpty(drillDownModel.getSortField())) {
            sb.append(" order by ").append(drillDownModel.getSortField());
            if (null != drillDownModel.getSortType() && drillDownModel.getSortType() == 1) {
                sb.append(" desc");
            }
        } else {
            //默认排序
            String defaultOrderBy;
            switch (drillDownModel.getDrillDownSceneEnum()) {
                case NF_DETAIL:
                    //如果是nf_url或者nf_other_log下钻，使用latest_relation_time进行排序,否则使用capture_time进行排序
                    if (DrillDownNormDataTypeEnum.NF_URL.getKey() == drillDownModel.getDataType() || DrillDownNormDataTypeEnum.NF_OTHER_LOG.getKey() == drillDownModel.getDataType()) {
                        defaultOrderBy = " order by latest_relation_time desc ";
                    } else {
                        defaultOrderBy = " order by capture_time desc ";
                    }
                    break;
                case LIS_DETAIL:
                    //如果是HTTP协议，则也使用latest_relation_time进行排序
                    if (DrillDownNormDataTypeEnum.HTTP.getKey() == drillDownModel.getDataType()) {
                        defaultOrderBy = " order by latest_relation_time desc ";
                    } else {
                        defaultOrderBy = " order by capture_time desc ";
                    }
                    break;
                case CONNECT_RECORD_DETAIL:
                case RADIUS_EXPANSION_DETAIL:
                case PHONE_EXPANSION_DETAIL:
                case PHONE_EXTRACT_SOURCE_DETAIL:
                case ATTACH_SOURCE_DETAIL:
                    defaultOrderBy = " order by capture_time desc ";
                    break;
                default:
                    defaultOrderBy = "";
            }
            sb.append(defaultOrderBy);
        }
    }

    private static void appendNetActionCondition(DrillDownModel drillDownModel, StringBuilder sb) {
        //根据网络类型条件生成where条件
        if (null != drillDownModel.getNetAction()) {
            //如果是VPN明细
            if (drillDownModel.getDrillDownSceneEnum().equals(DrillDownSceneEnum.VPN_DETAIL)) {
                String key = DrillDownNormDataTypeEnum.getByKey(drillDownModel.getDataType()).replaceAll("_vpn", "");
                appendNetAction(drillDownModel, sb, key);
            } else if ((drillDownModel.getDrillDownSceneEnum().equals(DrillDownSceneEnum.NF_DETAIL) ||
                    drillDownModel.getDrillDownSceneEnum().equals(DrillDownSceneEnum.APP_ARC_VISITOR_DETAIL) ||
                    drillDownModel.getDrillDownSceneEnum().equals(DrillDownSceneEnum.WEBSITE_ARC_VISITOR_DETAIL))
                    && drillDownModel.getNetAction() != 0) {
                //网络类型 0:全部 1：Alert 2：Reject 3：Log
                String key = DrillDownNormDataTypeEnum.getByKey(drillDownModel.getDataType());
                appendNetAction(drillDownModel, sb, key);
            }
        }
    }

    private static void appendNetAction(DrillDownModel drillDownModel, StringBuilder sb, String key) {
        switch (drillDownModel.getNetAction()) {
            case 1:
                sb.append(" AND net_action in (");
                for (NfNetActionEnum value : NfNetActionEnum.values()) {
                    if ("alert".equals(value.getNfType()) && value.getNetActionDes().contains(key)) {
                        sb.append(value.getNetAction()).append(",");
                    }
                }
                break;
            case 2:
                sb.append(" AND net_action in (");
                for (NfNetActionEnum value : NfNetActionEnum.values()) {
                    if ("reject".equals(value.getNfType()) && value.getNetActionDes().contains(key)) {
                        sb.append(value.getNetAction()).append(",");
                    }
                }
                break;
            case 3:
                sb.append(" AND net_action in (");
                for (NfNetActionEnum value : NfNetActionEnum.values()) {
                    if ("log".equals(value.getNfType()) && value.getNetActionDes().contains(key)) {
                        sb.append(value.getNetAction()).append(",");
                    }
                }
                break;
            default:
                sb.append(" AND net_action in (");
                for (NfNetActionEnum value : NfNetActionEnum.values()) {
                    sb.append(value.getNetAction()).append(",");
                }
        }
        sb.delete(sb.length() - 1, sb.length());
        sb.append(")");
    }

    private static void appendKeywordCondition(DrillDownModel drillDownModel, List<Map<String, Object>> searchHeaders, StringBuilder sb) {
        //根据keyWord条件生成where条件
        if (StringUtils.isNotBlank(drillDownModel.getKeyWord()) && !searchHeaders.isEmpty()) {

            // 如果为IM的详情查询，且scope为1，则将searchHeaders换为其他字段的列表，包含auth_account,strsrc_ip,dstsrc_ip
            if (DrillDownNormDataTypeEnum.IM.getKey() == drillDownModel.getDataType() && drillDownModel.getScope() != null
                    && 1 == drillDownModel.getScope()) {
                searchHeaders = searchHeaders.stream().filter(map -> "auth_account".equals(map.get("field")) || "strsrc_ip".equals(map.get("field")) || "strdst_ip".equals(map.get("field")))
                        .collect(Collectors.toList());
            }

            sb.append(" AND (");
            for (Map<String, Object> stringObjectMap : searchHeaders) {
                sb.append(stringObjectMap.get("field")).append(" like ").append("'%").append(drillDownModel.getKeyWord()).append("%'").append(" or ");
            }
            sb.delete(sb.length() - 3, sb.length());
            sb.append(")");
        }
    }

    private static void appendFiledCondition(List<Map<String, Object>> list, StringBuilder sb) {
        //根据字段配置生成doris查询明细sql，将list内的字段进行拼
        for (Map<String, Object> stringObjectMap : list) {
            //如果字段名称为position，需要特殊加引号处理，否则DAT侧会拦截sql
            if ("_position".equals(stringObjectMap.get("field"))) {
                sb.append("`position` as _position").append(",");
            } else {
                sb.append(stringObjectMap.get("field")).append(",");
            }
        }
        sb.deleteCharAt(sb.length() - 1);
    }

    private static void appendTimeCondition(DrillDownModel drillDownModel, StringBuilder sb) {
        //根据startDay和endDay条件生成where条件,注意前面已经校验过startDay和endDay不为空
        if (StringUtils.isNotBlank(drillDownModel.getStartDay()) && StringUtils.isNotBlank(drillDownModel.getEndDay())) {
            sb.append(" WHERE capture_day between ").append("'").append(drillDownModel.getStartDay()).append("'").append(" and ").append("'").append(drillDownModel.getEndDay()).append("'");
        }

        if (StringUtils.isNotBlank(drillDownModel.getStartTime()) && StringUtils.isNotBlank(drillDownModel.getEndTime())) {
            if (drillDownModel.getDataType().equals(DrillDownNormDataTypeEnum.HTTP.getKey())) {
                sb.append(String.format(" AND earliest_relation_time >= %s AND latest_relation_time  <= %s ", drillDownModel.getStartTime(), drillDownModel.getEndTime()));
            } else {
                sb.append(String.format(" AND capture_time >= %s AND capture_time <= %s ", drillDownModel.getStartTime(), drillDownModel.getEndTime()));
            }
        }
    }

    private static void appendSqlByScene(DrillDownModel drillDownModel, StringBuilder sb) {
        String code = drillDownModel.getDrillDownSceneEnum().getCode();
        if (DrillDownSceneEnum.CONNECT_RECORD_DETAIL.getCode().equals(code)) {
            // 通联记录下钻
            //根据arcAccount条件生成where条件
            if (StringUtils.isNotBlank(drillDownModel.getArcAccount())) {
                String calling = String.format("calling_number = '%s' ", drillDownModel.getArcAccount());
                String called = String.format("called_number = '%s'", drillDownModel.getArcAccount());

                if (StringUtils.isNotBlank(drillDownModel.getConnectedArea())) {
                    calling += " AND called_atrribution = '" + drillDownModel.getConnectedArea() + "'";
                    called += " AND calling_atrribution = '" + drillDownModel.getConnectedArea() + "'";
                }
                sb.append(" AND (( ").append(calling).append(") OR (").append(called).append(" ))");
            }
        } else if (DrillDownSceneEnum.VIRTUAL_ACCOUNT_DETAIL.getCode().equals(code)) {
            if (StringUtils.isNotBlank(drillDownModel.getVirtualAccount())) {
                if (DrillDownNormDataTypeEnum.EMAIL.getKey() == drillDownModel.getDataType()) {
                    sb.append(String.format(" AND lower(main_account) = '%s' ", drillDownModel.getVirtualAccount().toLowerCase()));
                } else {
                    sb.append(String.format(" AND main_account = '%s' ", drillDownModel.getVirtualAccount()));
                }
            }

            if (StringUtils.isNotBlank(drillDownModel.getVirtualAccountAppType())) {
                sb.append(String.format(" AND tool_type = '%s' ", drillDownModel.getVirtualAccountAppType()));
            }

            appendAuthInfoUseAuthField(drillDownModel, sb);
        } else if (DrillDownSceneEnum.RADIUS_EXPANSION_DETAIL.getCode().equals(code)) {
            // Radius档案 关系扩, IM档案 关系扩, Email档案 关系扩线
            if (ArcTypeEnum.IM.getKey().equals(drillDownModel.getArcType())) {
                imRelationExpansionConditionAppend(drillDownModel, sb, drillDownModel.getArcAccountType());
            } else {
                emailRelationExpansionConditionAppend(drillDownModel, sb);
            }
        } else if (DrillDownSceneEnum.PHONE_EXPANSION_DETAIL.getCode().equals(code)) {
            // 号码档案关系扩线
            if (Integer.valueOf(DataTypeEnum.EMAIL.getKey()).equals(drillDownModel.getDataType())) {
                emailRelationExpansionConditionAppend(drillDownModel, sb);
            } else if (Integer.valueOf(DataTypeEnum.IM.getKey()).equals(drillDownModel.getDataType())) {
                imRelationExpansionConditionAppend(drillDownModel, sb, drillDownModel.getVirtualAccountAppType());
            } else {
                phoneRelationExpansionConditionAppend(drillDownModel, sb);
            }
        } else if (DrillDownSceneEnum.LIS_DETAIL.getCode().equals(code) || DrillDownSceneEnum.NF_DETAIL.getCode().equals(code) || DrillDownSceneEnum.VPN_DETAIL.getCode().equals(code)) {
            if (DrillDownSceneEnum.VPN_DETAIL.getCode().equals(code)) {
                //如果是VPN的NF下钻，需要带上条件应用类型为proxytool
                sb.append(" AND app_type in ('proxytool','ProxyTool')");
                //根据时段信息生成where条件
                VpnTrendModel vpnTrendModel = new VpnTrendModel();
                vpnTrendModel.setStartDay(drillDownModel.getStartDay());
                vpnTrendModel.setEndDay(drillDownModel.getEndDay());
                vpnTrendModel.setTimes(drillDownModel.getTimes());
                //由于目前VPN数据是小时级压缩，目前只支持小时级别的下钻
                hourConditionHandle(vpnTrendModel, sb);
                //TODO 后续支持分钟级别的下钻
                //timeConditionHandle(vpnTrendModel, sb);
            }

            // 下面需要根据不同协议类型做特定的处理逻辑
            appendLisDetailByDataType(drillDownModel, sb);
        } else if (DrillDownSceneEnum.WEBSITE_ARC_VISITOR_DETAIL.getCode().equals(code) || DrillDownSceneEnum.APP_ARC_VISITOR_DETAIL.getCode().equals(code)) {
            // 网站和应用档案访问者下钻
            if (drillDownModel.getAuthAccount() != null) {
                sb.append(" AND auth_account = ").append("'").append(drillDownModel.getAuthAccount()).append("'");
            }

            if (StringUtils.isNotBlank(drillDownModel.getAuthAccountType()) && !"0".equals(drillDownModel.getAuthAccountType())) {
                sb.append(" AND auth_type = ").append(drillDownModel.getAuthAccountType());
            }

            if (StringUtils.isNotBlank(drillDownModel.getSrcCountry())) {
                sb.append(" AND src_ip_country = ").append("'").append(drillDownModel.getSrcCountry()).append("'");
            } else {
                sb.append(" AND src_ip_country = ''");
            }

            if (DrillDownSceneEnum.WEBSITE_ARC_VISITOR_DETAIL.getCode().equals(code)) {
                sb.append(" AND domain = ").append("'").append(drillDownModel.getArcAccount()).append("'");
            } else if (DrillDownSceneEnum.APP_ARC_VISITOR_DETAIL.getCode().equals(code)) {
                sb.append(" AND app_name = ").append("'").append(drillDownModel.getArcAccount()).append("'");
                sb.append(" AND app_type = ").append("'").append(drillDownModel.getArcAccountType()).append("'");
            }
        } else if (DrillDownSceneEnum.ATTACH_SOURCE_DETAIL.getCode().equals(code) || DrillDownSceneEnum.PHONE_EXTRACT_SOURCE_DETAIL.getCode().equals(code)) {
            attachScene(drillDownModel, sb, code);
        }
    }

    private static void phoneRelationExpansionConditionAppend(DrillDownModel drillDownModel, StringBuilder sb) {
        sb.append(String.format(" AND ((calling_number = '%s' AND called_number = '%s') OR (calling_number = '%s' AND called_number = '%s'))",
                drillDownModel.getSrcAccount(), drillDownModel.getDstAccount(), drillDownModel.getDstAccount(), drillDownModel.getSrcAccount()));
        sb.append(" AND spam_flag=0 ");
    }

    private static void emailRelationExpansionConditionAppend(DrillDownModel drillDownModel, StringBuilder sb) {
        sb.append(String.format(" AND ((lower(mail_from) = '%s' AND ( lower(mail_to) like '%%%s%%' OR lower(mail_cc) like '%%%s%%' OR lower(mail_bcc) like '%%%s%%' )) " + "OR (lower(mail_from) = '%s' AND ( lower(mail_to) like '%%%s%%' OR lower(mail_cc) like '%%%s%%' OR lower(mail_bcc) like '%%%s%%' )))",
                drillDownModel.getSrcAccount().toLowerCase(), drillDownModel.getDstAccount().toLowerCase(), drillDownModel.getDstAccount().toLowerCase(), drillDownModel.getDstAccount().toLowerCase(), drillDownModel.getDstAccount().toLowerCase(), drillDownModel.getSrcAccount().toLowerCase(), drillDownModel.getSrcAccount().toLowerCase(), drillDownModel.getSrcAccount().toLowerCase()));
        sb.append(" AND spam_flag=0 ");
    }

    private static void imRelationExpansionConditionAppend(DrillDownModel drillDownModel, StringBuilder sb, String appType) {
        sb.append(String.format(" AND ((from_id='%s' AND to_id = '%s') OR (from_id='%s' AND to_id = '%s'))", drillDownModel.getSrcAccount(),
                drillDownModel.getDstAccount(), drillDownModel.getDstAccount(), drillDownModel.getSrcAccount()));
        sb.append(String.format(" AND tool_type = '%s'", appType));
    }

    private static void attachScene(DrillDownModel drillDownModel, StringBuilder sb, String code) {
        //除了fax协议要根据calling_number或called_number查询，其他有文件的lis协议根据auth_account查询
        if (DrillDownNormDataTypeEnum.FAX.getKey() == drillDownModel.getDataType()) {
            sb.append(" AND (calling_number = ").append("'").append(drillDownModel.getArcAccount()).append("' OR called_number = ").append("'").append(drillDownModel.getArcAccount()).append("') ");
        } else if (DrillDownNormDataTypeEnum.EMAIL.getKey() == drillDownModel.getDataType() && DrillDownSceneEnum.PHONE_EXTRACT_SOURCE_DETAIL.getCode().equals(code)) {
            sb.append(" AND (lower(mail_from) like '%").append(drillDownModel.getVirtualAccount().toLowerCase()).append("%' OR lower(mail_to) like '%").append(drillDownModel.getVirtualAccount().toLowerCase())
                    .append("%' OR lower(mail_cc) like '%").append(drillDownModel.getVirtualAccount().toLowerCase()).append("%' OR lower(mail_bcc) like '%").append(drillDownModel.getVirtualAccount().toLowerCase())
                    .append("%' OR lower(main_account) like '%").append(drillDownModel.getVirtualAccount().toLowerCase())
                    .append("%') ");
            appendAuthInfoUseAuthField(drillDownModel, sb);
        } else {

            switch (drillDownModel.getArcType()) {
                case "2":
                    sb.append(" AND (lower(mail_from) like '%").append(drillDownModel.getArcAccount().toLowerCase()).append("%' OR lower(mail_to) like '%").append(drillDownModel.getArcAccount().toLowerCase())
                            .append("%' OR lower(mail_cc) like '%").append(drillDownModel.getArcAccount().toLowerCase()).append("%' OR lower(mail_bcc) like '%").append(drillDownModel.getArcAccount().toLowerCase())
                            .append("%' OR lower(main_account) like '%").append(drillDownModel.getArcAccount().toLowerCase())
                            .append("%') ");
                    break;
                case "6":
                    sb.append(" AND (main_account = '").append(drillDownModel.getArcAccount()).append("') ");
                    if (StringUtils.isNotEmpty(drillDownModel.getArcAccountType())) {
                        sb.append(" AND tool_type = " + drillDownModel.getArcAccountType());
                    }
                    break;
                case "1":
                case "5":
                case "8":
                    sb.append(" AND auth_account = '" + drillDownModel.getArcAccount() + "'");
                    if (StringUtils.isNotEmpty(drillDownModel.getArcAccountType())) {
                        sb.append(" AND auth_type = " + drillDownModel.getArcAccountType());
                    }
                    break;
                case "3":
                    // 网站档案无需拼接其他信息
                    break;
                default:
                    sb.append(" AND auth_account = " + drillDownModel.getArcAccount());
                    break;
            }
        }

        //附件明细查询根据filePath查询
        if (CollUtil.isNotEmpty(drillDownModel.getFilePathList())) {
            sb.append(" AND file_path in ( ");

            for (String filePath : drillDownModel.getFilePathList()) {
                sb.append("'").append(filePath).append("',");
            }
            sb.deleteCharAt(sb.length() - 1);
            sb.append(" ) ");
        }
    }

    private static void appendLisDetailByDataType(DrillDownModel drillDownModel, StringBuilder sb) {
        if (DrillDownNormDataTypeEnum.CALL.getKey() == drillDownModel.getDataType() || DrillDownNormDataTypeEnum.SMS.getKey() == drillDownModel.getDataType() || DrillDownNormDataTypeEnum.FAX.getKey() == drillDownModel.getDataType()) {
            //如果是SMS或者CALL或者FAX下钻场景，auth_account比对的字段为calling_number
            sb.append(" AND calling_number = ").append("'").append(drillDownModel.getArcAccount()).append("'");
        } else if (DrillDownNormDataTypeEnum.IM.getKey() == drillDownModel.getDataType()) {
            if (drillDownModel.getScope() != null && drillDownModel.getScope() == 1) {
                sb.append(" AND important_target_flag <> 0 ");
            }

            if (ArcTypeEnum.IM.getKey().equals(drillDownModel.getArcType())) {
                sb.append(String.format(" AND tool_type = '%s'", drillDownModel.getArcAccountType()));
                sb.append(" AND (main_account = ").append("'").append(drillDownModel.getArcAccount()).append("') ");
            }

            appendAuthInfoUseAuthField(drillDownModel, sb);
        } else if (DrillDownNormDataTypeEnum.EMAIL.getKey() == drillDownModel.getDataType()) {
            //根据arcAccount条件生成where条件
            if (StringUtils.isNotEmpty(drillDownModel.getEmailType())) {

                switch (drillDownModel.getEmailType()) {
                    case "outbox":
                        sb.append(" AND lower(mail_from) like '%" + drillDownModel.getArcAccount().toLowerCase() + "%' AND `action` = '32' ");
                        break;
                    case "inbox":
                        sb.append(" AND (lower(mail_to) like '%" + drillDownModel.getArcAccount().toLowerCase() + "%' OR lower(mail_cc) like '%" + drillDownModel.getArcAccount().toLowerCase() + "%' OR lower(mail_bcc) like '%" + drillDownModel.getArcAccount().toLowerCase() + "%') AND `action` = '31' ");
                        break;
                    case "draft":
                        sb.append(" AND lower(mail_from) like '%" + drillDownModel.getArcAccount().toLowerCase() + "%' AND `action` in ('49', 'UV') ");
                        break;
                    default:
                        break;
                }
            }

            // 这块应该是遗留代码可以去掉
            if (drillDownModel.getVirtualAccount() != null) {
                sb.append(String.format("AND (lower(mail_from) = '%s' OR ( lower(mail_to) like '%%%s%%' OR lower(mail_cc) like '%%%s%%' OR lower(mail_bcc) like '%%%s%%' ))",
                        drillDownModel.getVirtualAccount().toLowerCase(), drillDownModel.getVirtualAccount().toLowerCase(), drillDownModel.getVirtualAccount().toLowerCase(), drillDownModel.getVirtualAccount().toLowerCase()));
            }

            appendAuthInfoUseAuthField(drillDownModel, sb);
        } else {
            appendAuthInfoUseAuthField(drillDownModel, sb);
        }
    }

    private static void appendAuthInfoUseAuthField(DrillDownModel drillDownModel, StringBuilder sb) {
        // 这块主要做个兼容适配，后续下钻参数和字段不能存在二义性

        if (StringUtils.isNotBlank(drillDownModel.getArcAccount())) {
            if (drillDownModel.getAuthAccount() != null) {
                sb.append(" AND auth_account = ").append("'").append(drillDownModel.getAuthAccount()).append("'");
            } else {
                if (ArcTypeEnum.PHONE.getKey().equals(drillDownModel.getArcType()) || ArcTypeEnum.RADIUS.getKey().equals(drillDownModel.getArcType())
                        || ArcTypeEnum.FIXED_IP.getKey().equals(drillDownModel.getArcType())) {
                    sb.append(" AND auth_account = ").append("'").append(drillDownModel.getArcAccount()).append("'");
                }
            }
        }
        //根据arcAccountType条件生成where条件
        if (StringUtils.isNotBlank(drillDownModel.getAuthAccountType()) && !"0".equals(drillDownModel.getAuthAccountType())) {
            sb.append(" AND auth_type = ").append(drillDownModel.getAuthAccountType());
        } else if (StringUtils.isNotBlank(drillDownModel.getArcAccountType()) && !"0".equals(drillDownModel.getArcAccountType())) {
            if (ArcTypeEnum.PHONE.getKey().equals(drillDownModel.getArcType()) || ArcTypeEnum.RADIUS.getKey().equals(drillDownModel.getArcType()) || ArcTypeEnum.FIXED_IP.getKey().equals(drillDownModel.getArcType())) {
                sb.append(" AND auth_type = ").append(drillDownModel.getArcAccountType());
            }
        }
    }

    @Override
    public Object getTrend(TrendAndDistributeModel trendAndDistributeModel) {
        List<Map<String, Object>> result = new ArrayList<>();
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(new DateModel(trendAndDistributeModel.getStartDay(), trendAndDistributeModel.getEndDay(), trendAndDistributeModel.getDateOption()));
        DateUtils.validateParamMap(paramMap, trendAndDistributeModel.getCreateDay());
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT  SUM(behavior_num) as behaviorNum, UNIX_TIMESTAMP(capture_day) * 1000 AS captureTime");
        sb.append(" FROM ");
        if (DataSourceEnum.LIS == trendAndDistributeModel.getDataSourceEnum()) {
            sb.append(" ").append(dwsDbName).append(".dws_element_lis_protocol_info ");
        } else {
            sb.append(" ").append(dwsDbName).append(".dws_element_nf_protocol_info ");
        }
        //根据startDay和endDay条件生成where条件,注意前面已经校验过startDay和endDay不为空
        if (StringUtils.isNotBlank(paramMap.getStartDay()) && StringUtils.isNotBlank(paramMap.getEndDay())) {
            sb.append(" where capture_day between ").append("'").append(paramMap.getStartDay()).append("'").append(" and ").append("'").append(paramMap.getEndDay()).append("'");
        }
        //根据createDay条件生成where条件:固定IP档案创建日期，判断固定IP档案查询数据时间范围
        if (StringUtils.isNotBlank(trendAndDistributeModel.getCreateDay())) {
            sb.append(" AND capture_day >= ").append("'").append(trendAndDistributeModel.getCreateDay()).append("'");
        }
        //根据arcAccount条件生成where条件
        if (StringUtils.isNotBlank(trendAndDistributeModel.getArcAccount())) {
            sb.append(" AND auth_account = ").append("'").append(trendAndDistributeModel.getArcAccount()).append("'");
        }
        //根据arcAccountType条件生成where条件
        sb.append(" AND auth_type = ").append(trendAndDistributeModel.getArcAccountType());

        //根据dataType条件生成where条件
        if (StringUtils.isNotEmpty(trendAndDistributeModel.getDataType()) && !"0".equals(trendAndDistributeModel.getDataType())) {
            sb.append(String.format(" AND norm_data_type in (%s)", trendAndDistributeModel.getDataType()));
        }

        if (DataSourceEnum.LIS == trendAndDistributeModel.getDataSourceEnum()) {
            //屏蔽 AppCall
            sb.append(" AND norm_data_type <> '2109' ");
        }

        sb.append(" group by capture_day");

        List<Map<String, Object>> resultTmp = composeQueryService.queryForList(sb.toString(), "getTrend");
        //如果不存咋的天数需要补零处理
        if (resultTmp.size() != DateUtils.getBetweenDays(paramMap.getStartDay(), paramMap.getEndDay()) + 1) {
            Map<String, Object> resultMap = new HashMap<>(16);
            for (Map<String, Object> map : resultTmp) {
                resultMap.put(map.get("captureTime").toString(), map.get("behaviorNum"));
            }
            for (int i = 0; i <= DateUtils.getBetweenDays(paramMap.getStartDay(), paramMap.getEndDay()); i++) {
                String day = DateUtils.getAddDay(paramMap.getStartDay(), i);
                if (resultMap.get(DateUtils.getTimestampByDay(day).toString()) == null) {
                    Map<String, Object> map = new HashMap<>(16);
                    map.put("captureTime", DateUtils.getTimestampByDay(day));
                    map.put("behaviorNum", 0);
                    resultTmp.add(map);
                }
            }
        }
        resultTmp.sort(Comparator.comparing(map -> Long.parseLong(map.get("captureTime").toString())));
        for (Map<String, Object> map : resultTmp) {
            Map<String, Object> resultMap = new HashMap<>(16);
            resultMap.put("captureTime", map.get("captureTime"));
            resultMap.put("behaviorNum", map.get("behaviorNum"));
            result.add(resultMap);
        }
        return result;
    }

    @Override
    public Object getDistribution(TrendAndDistributeModel trendAndDistributeModel) {
        List<LisRecordDistributionEntity> result = new ArrayList<>();
        //根据条件生成对应的doris查询分布sql
        String sql = getDistributionSql(trendAndDistributeModel);
        List<Map<String, Object>> distributionResult = composeQueryService.queryForList(sql, "getDistribution");

        if (CollectionUtil.isEmpty(distributionResult)) {
            return result;
        }

        long total = distributionResult.stream()
                .mapToLong(map -> {
                    Object behaviorNumObj = map.get("behaviorNum");
                    if (behaviorNumObj == null) {
                        return 0L; // 默认值，处理 null 的情况
                    }
                    try {
                        return Long.parseLong(behaviorNumObj.toString());
                    } catch (NumberFormatException e) {
                        // 处理无法解析为 Long 的情况
                        return 0L;
                    }
                })
                .sum();

        long totalBehaviorNum = 0;
        for (Map<String, Object> map : distributionResult) {
            long behaviorNum = Long.parseLong(map.get("behaviorNum").toString());
            totalBehaviorNum += behaviorNum;
            //对协议类型进行翻译
            String dataType = map.get("dataType").toString();
            String dataTypeName = arcDrillDownFieldTranslateConfigService.fieldTranslate(CommonConstent.NORM_DATA_TYPE, dataType, trendAndDistributeModel.getLang()).toString();
            result.add(LisRecordDistributionEntity.builder().behaviorNum(behaviorNum).dataType(Integer.parseInt(map.get("dataType").toString())).dataTypeName(dataTypeName).rate(behaviorNum * 1.0 / total).build());
        }
        if (totalBehaviorNum == 0) {
            return result;
        }

        return result;
    }

    @Override
    public Object getNfActionDistribution(TrendAndDistributeModel trendAndDistributeModel) {
        List<NfActionDistributionEntity> result = new ArrayList<>();
        String sql = getDistributionSql(trendAndDistributeModel);
        List<Map<String, Object>> distributionResult = composeQueryService.queryForList(sql, "getNfActionDistribution");
        //获取不同协议的行为及分布情况
        AtomicLong totalBehavior = new AtomicLong(0L);
        AtomicLong totalLog = new AtomicLong(0L);
        AtomicLong totalRefuse = new AtomicLong(0L);
        AtomicLong totalWarn = new AtomicLong(0L);
        AtomicLong totalOther = new AtomicLong(0L);
        distributionResult.forEach(stringObjectMap -> {
            long behaviorNum = Integer.parseInt(stringObjectMap.get("behaviorNum").toString());
            Integer netAction = Integer.parseInt(stringObjectMap.get("netAction").toString());
            if (Objects.requireNonNull(NfNetActionEnum.getByCodeEnum(netAction)).getNfType().equals(NfNetActionEnum.NF_BBS_WEIBO_LOG.getNfType())) {
                totalLog.addAndGet(behaviorNum);
            } else if (Objects.requireNonNull(NfNetActionEnum.getByCodeEnum(netAction)).getNfType().equals(NfNetActionEnum.NF_BBS_WEIBO_REFUSE.getNfType())) {
                totalRefuse.addAndGet(behaviorNum);
            } else if (Objects.requireNonNull(NfNetActionEnum.getByCodeEnum(netAction)).getNfType().equals(NfNetActionEnum.NF_BBS_WEIBO_WARN.getNfType())) {
                totalWarn.addAndGet(behaviorNum);
            } else if (Objects.requireNonNull(NfNetActionEnum.getByCodeEnum(netAction)).getNfType().equals(NfNetActionEnum.NF_BBS_WEIBO_OTHER.getNfType())) {
                totalOther.addAndGet(behaviorNum);
            }
            totalBehavior.addAndGet(behaviorNum);
        });
        if (totalLog.get() > 0) {
            result.add(NfActionDistributionEntity.builder().behaviorNum(totalLog.get()).netAction("Log").rate(((double) totalLog.get() / totalBehavior.get())).build());
        }
        if (totalRefuse.get() > 0) {
            result.add(NfActionDistributionEntity.builder().behaviorNum(totalRefuse.get()).netAction("Reject").rate(((double) totalRefuse.get() / totalBehavior.get())).build());
        }
        if (totalWarn.get() > 0) {
            result.add(NfActionDistributionEntity.builder().behaviorNum(totalWarn.get()).netAction("Alert").rate(((double) totalWarn.get() / totalBehavior.get())).build());
        }
        if (totalOther.get() > 0) {
            result.add(NfActionDistributionEntity.builder().behaviorNum(totalOther.get()).netAction("Other").rate(((double) totalOther.get() / totalBehavior.get())).build());
        }
        return result;
    }

    /**
     * 根据条件生成对应的doris查询分布sql
     *
     * @param trendAndDistributeModel lis或nf查询条件
     * @return doris查询分布sql
     */
    private String getDistributionSql(TrendAndDistributeModel trendAndDistributeModel) {
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(new DateModel(trendAndDistributeModel.getStartDay(), trendAndDistributeModel.getEndDay(), trendAndDistributeModel.getDateOption()));
        DateUtils.validateParamMap(paramMap, trendAndDistributeModel.getCreateDay());
        StringBuilder sb = new StringBuilder();
        if (DataSourceEnum.LIS == trendAndDistributeModel.getDataSourceEnum()) {
            sb.append("SELECT SUM(behavior_num) as behaviorNum, norm_data_type as dataType FROM ");
            sb.append(" ").append(dwsDbName).append(".dws_element_lis_protocol_info ");
        } else {
            sb.append("SELECT SUM(behavior_num) as behaviorNum, net_action as netAction FROM ");
            sb.append(" ").append(dwsDbName).append(".dws_element_nf_protocol_info ");
        }
        //根据startDay和endDay条件生成where条件,注意前面已经校验过startDay和endDay不为空
        if (StringUtils.isNotBlank(paramMap.getStartDay()) && StringUtils.isNotBlank(paramMap.getEndDay())) {
            sb.append(" WHERE capture_day between ").append("'").append(paramMap.getStartDay()).append("'").append(" and ").append("'").append(paramMap.getEndDay()).append("'");
        }
        //根据createDay条件生成where条件:固定IP档案创建日期，判断固定IP档案查询数据时间范围
        if (StringUtils.isNotBlank(trendAndDistributeModel.getCreateDay())) {
            sb.append(" AND capture_day >= ").append("'").append(trendAndDistributeModel.getCreateDay()).append("'");
        }
        //根据arcAccount条件生成where条件
        if (StringUtils.isNotBlank(trendAndDistributeModel.getArcAccount())) {
            sb.append(" AND auth_account = ").append("'").append(trendAndDistributeModel.getArcAccount()).append("'");
        }
        //根据authType条件生成where条件
        if (null != trendAndDistributeModel.getArcAccountType()) {
            sb.append(" AND auth_type = ").append(trendAndDistributeModel.getArcAccountType());
        }
        //根据dataType条件生成where条件
        if (StringUtils.isNotBlank(trendAndDistributeModel.getDataType()) && !"0".equals(trendAndDistributeModel.getDataType())) {
            sb.append(String.format(" AND norm_data_type in (%s)", trendAndDistributeModel.getDataType()));
        }

        // AppCall 需要过滤
        if (DataSourceEnum.LIS == trendAndDistributeModel.getDataSourceEnum()) {
            sb.append(" AND norm_data_type <> '2109' ");
        }

        if (DataSourceEnum.LIS == trendAndDistributeModel.getDataSourceEnum()) {
            sb.append(" group by norm_data_type order by behaviorNum desc");
        } else {
            sb.append(" group by net_action order by behaviorNum desc");
        }
        return sb.toString();
    }

    public Object vpnUseDayTrend(VpnTrendModel vpnTrendModel) {
        DateModel dateModel = new DateModel(vpnTrendModel.getStartDay(), vpnTrendModel.getEndDay(), vpnTrendModel.getDateOption());
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        DateUtils.validateParamMap(paramMap, vpnTrendModel.getCreateDay());
        //如果用户传未传入时段信息
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT " +
//                "SUM(CASE WHEN block_flag = 1 THEN behavior_num ELSE 0 END) AS alertNum, " +  // Alert
                "SUM(CASE WHEN block_flag = 2 THEN behavior_num ELSE 0 END) AS rejectNum, " + // Reject
                "SUM(CASE WHEN block_flag = 3 THEN behavior_num ELSE 0 END) AS logNum, " +    // Log
                "capture_day AS captureDay FROM ");
        sb.append(" ").append(dwsDbName).append(".dws_element_vpn_info_hour WHERE auth_account = ").append("'").append(vpnTrendModel.getArcAccount()).append("'");
        timeConditionHandle(vpnTrendModel, sb);
        sb.append(" and auth_type = ").append(vpnTrendModel.getArcAccountType()).append(" and capture_day BETWEEN ").append("'").append(paramMap.getStartDay()).append("'").append(" AND ").append("'").append(paramMap.getEndDay()).append("'").append(" GROUP BY auth_account,capture_day ORDER BY capture_day");
        Map<String, Object> res = new HashMap<>(16);
        res.put("list", composeQueryService.queryForList(sb.toString(), "vpnUseDayTrend"));
        //对结果进行补零处理，如果某天没有数据，需要将该天数据补零
        List<Map<String, Object>> list = (List<Map<String, Object>>) res.get("list");
        Map<String, Object> resultMap = new HashMap<>(16);
        for (Map<String, Object> map : list) {
            resultMap.put(map.get("captureDay").toString(), map);
        }
        for (int i = 0; i <= DateUtils.getBetweenDays(paramMap.getStartDay(), paramMap.getEndDay()); i++) {
            String day = DateUtils.getAddDay(paramMap.getStartDay(), i);
            if (resultMap.get(day) == null) {
                Map<String, Object> map = new HashMap<>(16);
                map.put("captureDay", day);
//                map.put("alertNum", 0);
                map.put("rejectNum", 0);
                map.put("logNum", 0);
                list.add(map);
            }
        }
        //对补零结果进行排序
        list.sort(Comparator.comparing(map -> map.get("captureDay").toString()));
        list.forEach(item -> {
            String date = (String) item.get("captureDay");
            item.put("captureDay", DateUtils.toddmmyyyy(date));
        });
        return res;
    }

    /**
     * @param vpnTrendModel 小时
     * @return 返回结果
     */

    public Object vpnUseHourTrend(VpnTrendModel vpnTrendModel) {
        DateModel dateModel = new DateModel(vpnTrendModel.getStartDay(), vpnTrendModel.getEndDay(), vpnTrendModel.getDateOption());
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        DateUtils.validateParamMap(paramMap, vpnTrendModel.getCreateDay());
        StringBuilder sb = new StringBuilder();
        //根据阻断标志查询阻断及允许分别的次数,拼接sql
        sb.append("SELECT capture_hour AS capture_hour, " +
                "FLOOR(capture_minute / 30) AS half_hour, " +
//                "SUM(CASE WHEN block_flag = 1 THEN behavior_num ELSE 0 END) AS alertNum, " +  // Alert
                "SUM(CASE WHEN block_flag = 2 THEN behavior_num ELSE 0 END) AS rejectNum, " + // Reject
                "SUM(CASE WHEN block_flag = 3 THEN behavior_num ELSE 0 END) AS logNum " +     // Log
                "FROM ");
        sb.append(" ").append(dwsDbName).append(".dws_element_vpn_info_hour WHERE capture_day BETWEEN ").append("'").append(paramMap.getStartDay()).append("'").append(" AND ").append("'").append(paramMap.getEndDay()).append("'");
        sb.append(" AND auth_account = ").append("'").append(vpnTrendModel.getArcAccount()).append("'");
        sb.append(" AND auth_type = ").append(vpnTrendModel.getArcAccountType());
        timeConditionHandle(vpnTrendModel, sb);
        sb.append(" GROUP BY capture_hour, half_hour ORDER BY  capture_hour, half_hour");
        List<Map<String, Object>> result = composeQueryService.queryForList(sb.toString(), "vpnUseHourTrend");
        Map<String, Object> res = new HashMap<>(16);
        //从结果中分别获取time，behaviorNum，blockNum，且time的格式为HH：MM，MM为00或30，MM由half_hour字段及capture_hour字段组成
        List<Map<String, Object>> list = new ArrayList<>();
        for (Map<String, Object> map : result) {
            Map<String, Object> resultMap = new HashMap<>(16);
            double time = Double.parseDouble(map.get("half_hour").toString()) * 30;
            resultMap.put("time", map.get("capture_hour") + ":" + (time == 0 ? "00" : "30"));
//            resultMap.put("alertNum", map.get("alertNum"));
            resultMap.put("rejectNum", map.get("rejectNum"));
            resultMap.put("logNum", map.get("logNum"));
            list.add(resultMap);
        }
        res.put("list", list);
        //对结果进行补零处理，如果某半小时没有数据，需要将该半小时数据补零,只对中间缺失的数据进行补零
        Map<String, Object> resultMap = new HashMap<>(16);
        for (Map<String, Object> map : list) {
            resultMap.put(map.get("time").toString(), map);
        }

        int start = 0;
        int end = 47;
        if (StringUtils.isNotEmpty(vpnTrendModel.getTimes())) {

            String[] parts = vpnTrendModel.getTimes().split("-");
            String[] startParts = parts[0].split("\\.");
            if (startParts.length == 2) {
                start = Integer.parseInt(startParts[0]) * 2 + (Integer.parseInt(startParts[1]) / 29);
            } else {
                start = Integer.parseInt(startParts[0]) * 2;
            }

            String[] endParts = parts[1].split("\\.");
            if (endParts.length == 2) {
                end = Integer.parseInt(endParts[0]) * 2 + (Integer.parseInt(endParts[1]) / 29);
            } else {
                end = Integer.parseInt(endParts[0]) * 2;
            }
        }
        //对有数据的时间段进行补零处理
        for (int i = start; i <= end; i++) {
            String time = i / 2 + ":" + (i % 2 == 0 ? "00" : "30");
            if (resultMap.get(time) == null) {
                Map<String, Object> map = new HashMap<>(16);
                map.put("time", time);
//                map.put("alertNum", 0);
                map.put("rejectNum", 0);
                map.put("logNum", 0);
                list.add(map);
            }
        }
        //对补零结果进行排序.并且time(HH:mm)要转换成时间再对比
        list.sort(Comparator.comparing(time -> Integer.parseInt(time.get("time").toString().split(":")[0]) * 60 + Integer.parseInt(time.get("time").toString().split(":")[1])));
        return res;
    }


    @Override
    public Object getHighFrequencyWordsRank(HighFrequencyWordsModel highFrequencyWordsModel) {
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(new DateModel(highFrequencyWordsModel.getStartDay(), highFrequencyWordsModel.getEndDay(), highFrequencyWordsModel.getDateOption()));
        DateUtils.validateParamMap(paramMap, highFrequencyWordsModel.getCreateDay());

        String methodName = "";
        if (Integer.valueOf(ArcTypeEnum.EMAIL.getKey()).equals(highFrequencyWordsModel.getArcType())) {
            methodName = BusinessCodeEnum.EMAIL_HIGH_FREQUENCY_WORDS_RANK.getValue();
        } else {
            methodName = BusinessCodeEnum.RADIUS_HIGH_FREQUENCY_WORDS_RANK.getValue();
        }

        String redisKey = arcCommonService.getRedisKeyByDay(highFrequencyWordsModel.getArcId(), methodName, highFrequencyWordsModel.getArcId() + highFrequencyWordsModel);
        if (redisOps.hasKey(redisKey) && redisOps.get(redisKey) != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(redisOps.get(redisKey));
        }
        getParmMap(highFrequencyWordsModel, paramMap);
        PageWarpEntity pageWarpEntity = new PageWarpEntity(highFrequencyWordsModel.getOnPage(), highFrequencyWordsModel.getSize(), highFrequencyWordsModel.getSortField(), highFrequencyWordsModel.getSortType());
        pageWarpEntity.setSortField("behaviorNum");
        pageWarpEntity.setSortType(1);

        List<Map<String, Object>> highFrequencyWordsRank = arcCommonService.getCommonServiceListResult(methodName, paramMap, pageWarpEntity);
        Map<String, Object> res = new HashMap<>(16);
        res.put("list", highFrequencyWordsRank);
        res.put("total", arcCommonService.getCommonServiceCountResult(methodName, paramMap, true));
        redisOps.set(redisKey, res, DateUtils.getRedisExpireTime());
        return res;
    }

    private Map<String, Object> getParmMap(HighFrequencyWordsModel highFrequencyWordsModel, CommonParamUtil.ParamMap paramMap) {
        paramMap.put("arcAccount", highFrequencyWordsModel.getArcAccount());
        paramMap.put("arcAccountType", highFrequencyWordsModel.getArcAccountType());
        paramMap.put("createDay", highFrequencyWordsModel.getCreateDay());
        if (3 == highFrequencyWordsModel.getDataSource()) {
            paramMap.put("dataSource", "1,2");
        } else {
            paramMap.put("dataSource", highFrequencyWordsModel.getDataSource());
        }
        return paramMap;
    }

    @Override
    public Object getHighFrequencyWordsTrend(HighFrequencyWordsModel highFrequencyWordsModel) {
        DateModel dateModel = new DateModel(highFrequencyWordsModel.getStartDay(), highFrequencyWordsModel.getEndDay(), highFrequencyWordsModel.getDateOption());
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        DateUtils.validateParamMap(paramMap, highFrequencyWordsModel.getCreateDay());

        String methodName = "";
        if (Integer.valueOf(ArcTypeEnum.EMAIL.getKey()).equals(highFrequencyWordsModel.getArcType())) {
            methodName = BusinessCodeEnum.EMAIL_HIGH_FREQUENCY_WORDS_TREND.getValue();
        } else {
            methodName = BusinessCodeEnum.RADIUS_HIGH_FREQUENCY_WORDS_TREND.getValue();
        }

        String redisKey = arcCommonService.getRedisKeyByDay(highFrequencyWordsModel.getArcId(), methodName, highFrequencyWordsModel);
        if (redisOps.hasKey(redisKey) && redisOps.get(redisKey) != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(redisOps.get(redisKey));
        }

        CommonParamUtil.ParamMap params = CommonParamUtil.buildCommonTimeParam(paramMap.getStartDay(), paramMap.getEndDay());
        params.put("arcAccount", highFrequencyWordsModel.getArcAccount());
        params.put("arcAccountType", highFrequencyWordsModel.getArcAccountType());
        params.put("createDay", highFrequencyWordsModel.getCreateDay());

        if (3 == highFrequencyWordsModel.getDataSource()) {
            params.put("dataSource", "1,2");
        } else {
            params.put("dataSource", highFrequencyWordsModel.getDataSource());
        }
        List<Map<String, Object>> highFrequencyWordsTrendList = arcCommonService.getCommonServiceListResult(methodName, params);
        //将对象highFrequencyWordsTrendList中的captureTime字段改为time
        //对结果进行补零处理，如果某天没有数据，需要将该天数据补零
        Map<String, Object> resultMap = new HashMap<>(16);

        if (CollectionUtil.isNotEmpty(highFrequencyWordsTrendList)) {
            for (Map<String, Object> map : highFrequencyWordsTrendList) {
                map.put("time", map.get("captureTime"));
                map.remove("captureTime");
            }

            for (Map<String, Object> map : highFrequencyWordsTrendList) {
                resultMap.put(map.get("time").toString(), map);
            }

            for (int i = 0; i <= DateUtils.getBetweenDays(paramMap.getStartDay(), paramMap.getEndDay()); i++) {
                String day = DateUtils.getAddDay(paramMap.getStartDay(), i);
                if (resultMap.get(DateUtils.getTimestampByDay(day).toString()) == null) {
                    Map<String, Object> map = new HashMap<>(16);
                    map.put("time", DateUtils.getTimestampByDay(day));
                    map.put("behaviorNum", 0);

                    highFrequencyWordsTrendList.add(map);
                }
            }
            //对补零结果进行排序
            highFrequencyWordsTrendList.sort(Comparator.comparing(map -> Long.parseLong(map.get("time").toString())));
        }

        Long expireRedisTime = DateUtils.getRedisTime(paramMap.getEndDay(), 0L);
        redisOps.set(redisKey, highFrequencyWordsTrendList, expireRedisTime);
        return highFrequencyWordsTrendList;
    }

    @Override
    public Object getBankCardInfo(HighFrequencyWordsModel highFrequencyWordsModel) {
        String methodName = BusinessCodeEnum.BANK_CARD_INFO.getValue();
        String redisKey = arcCommonService.getRedisKeyByDay(highFrequencyWordsModel.getArcId(), methodName, highFrequencyWordsModel);
        if (redisOps.hasKey(redisKey) && redisOps.get(redisKey) != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(redisOps.get(redisKey));
        }
        Map<String, Object> params = new HashMap<>(16);
        //如果排序字段不为空，对结果进行排序
        if (StringUtils.isNotBlank(highFrequencyWordsModel.getSortField())) {
            params.put("sortField", highFrequencyWordsModel.getSortField());
            //1=降序；2=升序
            params.put("sortType", highFrequencyWordsModel.getSortType() == 1 ? "desc" : "asc");
        } else {
            params.put("sortField", "capture_day");
            params.put("sortType", "desc");
        }
        params.put("arcAccount", highFrequencyWordsModel.getArcAccount());
        params.put("startDay", highFrequencyWordsModel.getStartDay());
        params.put("endDay", highFrequencyWordsModel.getEndDay());
        params.put("onPage", (highFrequencyWordsModel.getOnPage() - 1) * highFrequencyWordsModel.getSize());
        params.put("size", highFrequencyWordsModel.getSize());
        params.put("dws", dwsDbName);
        List<Map<String, Object>> bankCardInfoList = arcCommonService.getCommonServiceListResult(methodName, params);

        String methodTotalName = BusinessCodeEnum.BANK_CARD_INFO_TOTAL.getValue();
        long total = arcCommonService.getCommonServiceCountResult(methodTotalName, params, true);

        Map<String, Object> res = new HashMap<>(16);
        res.put("list", bankCardInfoList);
        res.put("total", total);
        Long expireRedisTime = DateUtils.getRedisTime(highFrequencyWordsModel.getEndDay(), 0L);
        redisOps.set(redisKey, res, expireRedisTime);
        return res;
    }

    @Override
    public Object getFlightInfo(HighFrequencyWordsModel highFrequencyWordsModel) {
        String methodName = BusinessCodeEnum.FLIGHT_INFO.getValue();
        String redisKey = arcCommonService.getRedisKeyByDay(highFrequencyWordsModel.getArcId(), methodName, highFrequencyWordsModel);
        if (redisOps.hasKey(redisKey) && redisOps.get(redisKey) != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(redisOps.get(redisKey));
        }
        Map<String, Object> params = new HashMap<>(16);
        //如果排序字段不为空，对结果进行排序
        if (StringUtils.isNotBlank(highFrequencyWordsModel.getSortField())) {
            params.put("sortField", highFrequencyWordsModel.getSortField());
            //1=降序；2=升序
            params.put("sortType", highFrequencyWordsModel.getSortType() == 1 ? "desc" : "asc");
        } else {
            params.put("sortField", "captureTime");
            params.put("sortType", "desc");
        }
        params.put("arcAccount", highFrequencyWordsModel.getArcAccount());
        params.put("startDay", highFrequencyWordsModel.getStartDay());
        params.put("endDay", highFrequencyWordsModel.getEndDay());
        params.put("onPage", (highFrequencyWordsModel.getOnPage() - 1) * highFrequencyWordsModel.getSize());
        params.put("size", highFrequencyWordsModel.getSize());
        params.put("dws", dwsDbName);
        List<Map<String, Object>> flightInfoList = arcCommonService.getCommonServiceListResult(methodName, params);
        Map<String, Object> res = new HashMap<>(16);
        res.put("list", flightInfoList);
        String methodTotalName = BusinessCodeEnum.FLIGHT_INFO_TOTAL.getValue();
        res.put("total", arcCommonService.getCommonServiceCountResult(methodTotalName, params, true));
        Long expireRedisTime = DateUtils.getRedisTime(highFrequencyWordsModel.getEndDay(), 0L);
        redisOps.set(redisKey, res, expireRedisTime);
        return res;
    }

    @Override
    public Object arcUpdateBasicInfo(String userId, String arcId, Integer arcType, String name) {
        boolean result1, result2;
        ArcRemarkEntity arcRemarkEntity = new ArcRemarkEntity();
        arcRemarkEntity.setArcId(arcId);
        arcRemarkEntity.setExpansionArcId(arcId);
        arcRemarkEntity.setRemark(name);
        arcRemarkEntity.setUserId(Integer.valueOf(userId));
        arcRemarkEntity.setCreateTime(System.currentTimeMillis());
        ReturnModel<?> returnModel = userRelationDataService.remarkSave(arcRemarkEntity, userId);
        result1 = returnModel.getCode() == ReturnCode.SUCCESS.getCode();

        //更新ES中的档案基础信息
        ArcUpdateEsModel arcUpdateEsModel = new ArcUpdateEsModel();
        arcUpdateEsModel.setArchive_alias(name);

        //更新档案信息
        String resourceName = getEsResourceNameByArcType(Objects.requireNonNull(ArcTypeEnum.getArcTypeByKey(arcType.toString())));
        result2 = esDataOperateService.updateDataById(resourceName, ES_ARC_ID_FIELD, arcId, arcUpdateEsModel);

        //更新缓存中档案别名信息
        if (result1 && result2) {
            redisOps.set(UPDATE_ARC_NAME_KEY + arcId, name, DateUtils.getRedisExpireTime());
        }

        return result1 && result2;
    }


    public Object vpnUseRank(VpnTrendModel vpnTrendModel) {
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(new DateModel(vpnTrendModel.getStartDay(), vpnTrendModel.getEndDay(), vpnTrendModel.getDateOption()));
        DateUtils.validateParamMap(paramMap, vpnTrendModel.getCreateDay());

        StringBuilder totalSql = new StringBuilder("SELECT SUM(behavior_num) as cnt FROM");
        totalSql.append(" ").append(dwsDbName).append(".dws_element_vpn_info_hour ");
        blockConditionHandle(vpnTrendModel, paramMap, totalSql);
        totalSql.append(" AND auth_account = ").append("'").append(vpnTrendModel.getArcAccount()).append("'");
        totalSql.append(" AND auth_type = ").append(vpnTrendModel.getArcAccountType());
        //时段过滤
        timeConditionHandle(vpnTrendModel, totalSql);

        Long total = tianHeDorisTemplate.queryForCount(totalSql.toString());

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" SELECT app_name as appName, SUM(behavior_num) as behaviorNum FROM ");
        stringBuilder.append(dwsDbName).append(".dws_element_vpn_info_hour ");
        //查询VPN使用次数排名
        blockConditionHandle(vpnTrendModel, paramMap, stringBuilder);
        //时段过滤
        timeConditionHandle(vpnTrendModel, stringBuilder);
        //auth_account和auth_type过滤
        stringBuilder.append(" AND auth_account = ").append("'").append(vpnTrendModel.getArcAccount()).append("'");
        stringBuilder.append(" AND auth_type = ").append(vpnTrendModel.getArcAccountType());
        stringBuilder.append("  GROUP BY app_name order by behaviorNum DESC limit 4");
        List<Map<String, Object>> vpnRankResult = composeQueryService.queryForList(stringBuilder.toString(), "vpnUseRank");
        if (total == 0 || vpnRankResult.isEmpty()) {
            return VpnUseRankEntity.builder().rank(new ArrayList<>()).trend(new ArrayList<>()).build();
        }

        int top4Num = vpnRankResult.stream().mapToInt(vpn -> Integer.parseInt(vpn.get("behaviorNum").toString())).sum();
        // 基于total计算百分比
        vpnRankResult.forEach(vpn -> {
            vpn.put("percentage", Double.parseDouble(vpn.get("behaviorNum").toString()) / total);
        });
        List<String> top4Apps = new ArrayList<>();
        //对于排名前4后的数据统一归为others
        vpnRankResult.forEach(stringObjectMap -> {
            top4Apps.add(stringObjectMap.get("appName").toString());
        });

        //如果others的数据不为空，需要加入到vpnRankResult中
        if (total - top4Num != 0) {
            Map<String, Object> otherMap = new HashMap<>();
            otherMap.put("appName", "others");
            otherMap.put("behaviorNum", total - top4Num);
            otherMap.put("percentage", (total - top4Num) / (total * 1.0));
            vpnRankResult.add(otherMap);
        }
        List<VpnUseRankEntity.TrendEntity> trend = new ArrayList<>();
        //查询VPN使用每日趋势,需要查询app_name的behavior_num排名前四的数据，按日分组
        top4Apps.forEach(app -> {
            StringBuilder stringBuilder_trend = new StringBuilder();
            stringBuilder_trend.append(" SELECT capture_day as captureDay, SUM(behavior_num) as behaviorNum from");
            stringBuilder_trend.append(" ").append(dwsDbName).append(".dws_element_vpn_info_hour ");
            blockConditionHandle(vpnTrendModel, paramMap, stringBuilder_trend);
            timeConditionHandle(vpnTrendModel, stringBuilder_trend);
            //增加app_name过滤条件
            stringBuilder_trend.append(" and app_name = ").append("'").append(app).append("'");
            //auth_account和auth_type过滤
            stringBuilder_trend.append(" AND auth_account = ").append("'").append(vpnTrendModel.getArcAccount()).append("'");
            stringBuilder_trend.append(" AND auth_type = ").append(vpnTrendModel.getArcAccountType());
            stringBuilder_trend.append(" GROUP BY capture_day ORDER BY capture_day");
            List<Map<String, Object>> vpnTrendResult = composeQueryService.queryForList(stringBuilder_trend.toString(), "vpnUseRank");
            trend.add(VpnUseRankEntity.TrendEntity.builder().appName(app).detail(vpnTrendResult).build());
        });
        //查询每日趋势，不包含前四app_name的数据
        StringBuilder stringBuilderTrendWithoutTop4 = new StringBuilder();
        stringBuilderTrendWithoutTop4.append("SELECT capture_day as captureDay, SUM(behavior_num) as behaviorNum from");
        stringBuilderTrendWithoutTop4.append(" ").append(dwsDbName).append(".dws_element_vpn_info_hour");
        //增加app_name过滤条件
        blockConditionHandle(vpnTrendModel, paramMap, stringBuilderTrendWithoutTop4);
        timeConditionHandle(vpnTrendModel, stringBuilderTrendWithoutTop4);
        //auth_account和auth_type过滤
        stringBuilderTrendWithoutTop4.append(" AND auth_account = ").append("'").append(vpnTrendModel.getArcAccount()).append("'");
        stringBuilderTrendWithoutTop4.append(" AND auth_type = ").append(vpnTrendModel.getArcAccountType());
        stringBuilderTrendWithoutTop4.append(" and app_name not in ( ").append(top4Apps.stream().map(app -> "'" + app + "'").collect(Collectors.joining(","))).append(")");
        stringBuilderTrendWithoutTop4.append(" GROUP BY capture_day ORDER BY capture_day");
        List<Map<String, Object>> vpnEveryDayTrendResult = composeQueryService.queryForList(stringBuilderTrendWithoutTop4.toString(), "vpnUseRank");
        //计算others的趋势，当others的数据不为空时，需要加入到trend中
        if (!vpnEveryDayTrendResult.isEmpty()) {
            trend.add(VpnUseRankEntity.TrendEntity.builder().appName("others").detail(vpnEveryDayTrendResult).build());
        }
        //对每日趋势数据进行补零处理，如果某天没有数据，需要将该天数据补零
        trend.forEach(trendEntity -> {
            Map<String, Object> resultMap = new HashMap<>(16);
            for (Map<String, Object> map : trendEntity.getDetail()) {
                resultMap.put(map.get("captureDay").toString(), map);
            }
            for (int i = 0; i <= DateUtils.getBetweenDays(paramMap.getStartDay(), paramMap.getEndDay()); i++) {
                String day = DateUtils.getAddDay(paramMap.getStartDay(), i);
                if (resultMap.get(day) == null) {
                    Map<String, Object> map = new HashMap<>(16);
                    map.put("captureDay", day);
                    map.put("behaviorNum", 0);
                    trendEntity.getDetail().add(map);
                }
            }
        });
        //对trend数据进行时间排序
        trend.forEach(trendEntity -> trendEntity.getDetail().sort(Comparator.comparing(map -> map.get("captureDay").toString())));
        trend.forEach(item -> {
            item.getDetail().forEach(entity -> {
                String date = entity.get("captureDay").toString();
                entity.put("captureDay", DateUtils.toddmmyyyy(date));
            });
        });
        return VpnUseRankEntity.builder().rank(vpnRankResult).trend(trend).build();
    }

    private static void hourConditionHandle(VpnTrendModel vpnTrendModel, StringBuilder stringBuilder) {
        if (StringUtils.isEmpty(vpnTrendModel.getTimes())) {
            return;
        }
        //解析时段信息（时段格式：多时段使用逗号进行拼接，例如1-3,2-5,7-9,12-14,18-20），也可能是1.5-3.5，2.5-5.5，7.5-9.5，12.5-14.5，18.5-20.5
        String[] hours = vpnTrendModel.getTimes().split(",");
        //首先将hours信息转换为具体时间戳，根据startDay和endDay信息
        stringBuilder.append(" AND (");
        for (String hour : hours) {
            String[] split = hour.split("-");
            String startTime = split[0];
            String endTime = split[1];
            Integer startHour = 0, endHour = 0;
            Integer startMinute = 0, endMinute = 0;
            //如果时段是分钟级，需要使用不同方式
            if (startTime.split("\\.").length > 1) {
                startHour = Integer.valueOf(startTime.split("\\.")[0]);
                startMinute = Integer.valueOf(startTime.split("\\.")[1]);
            } else {
                startHour = Integer.valueOf(startTime);
            }
            if (endTime.split("\\.").length > 1) {
                endHour = Integer.valueOf(endTime.split("\\.")[0]);
                endMinute = Integer.valueOf(endTime.split("\\.")[1]);
            } else {
                endHour = Integer.valueOf(endTime);
            }
            stringBuilder.append("( ( capture_hour * 60 + capture_minute BETWEEN ").append(startHour * 60 + startMinute).append(" AND ").append(endHour * 60 + endMinute).append(")");
            stringBuilder.append(")").append(" OR ");
        }
        stringBuilder.delete(stringBuilder.length() - 3, stringBuilder.length());
        stringBuilder.append(")");
    }


    private static void timeConditionHandle(VpnTrendModel vpnTrendModel, StringBuilder stringBuilder) {
        if (StringUtils.isEmpty(vpnTrendModel.getTimes())) {
            return;
        }
        //解析时段信息（时段格式：多时段使用逗号进行拼接，例如1-3,2-5,7-9,12-14,18-20），也可能是1.5-3.5，2.5-5.5，7.5-9.5，12.5-14.5，18.5-20.5
        List<String> newTimes = Arrays.asList(vpnTrendModel.getTimes().split(","));
        //首先将hours信息转换为具体时间戳，根据startDay和endDay信息
        stringBuilder.append(" AND (");
        for (String time : newTimes) {
            String[] split = time.split("-");
            String startTime = split[0];
            String endTime = split[1];
            Integer startHour = 0, endHour = 0;
            Integer startMinute = 0, endMinute = 0;
            //如果时段是分钟级，需要使用不同方式
            if (startTime.split("\\.").length > 1) {
                startHour = Integer.valueOf(startTime.split("\\.")[0]);
                startMinute = Integer.valueOf(startTime.split("\\.")[1]);
            } else {
                startHour = Integer.valueOf(startTime);
            }
            if (endTime.split("\\.").length > 1) {
                endHour = Integer.valueOf(endTime.split("\\.")[0]);
                endMinute = Integer.valueOf(endTime.split("\\.")[1]);
            } else {
                endHour = Integer.valueOf(endTime);
            }
            stringBuilder.append("( ( capture_hour * 60 + capture_minute BETWEEN ").append(startHour * 60 + startMinute).append(" AND ").append(endHour * 60 + endMinute).append(")");
            stringBuilder.append(")").append(" OR ");
        }
        stringBuilder.delete(stringBuilder.length() - 3, stringBuilder.length());
        stringBuilder.append(")");
    }

    private static List<String> getTime(VpnTrendModel vpnTrendModel) {
        List<String> times = vpnTrendModel.getTimes().split(",").length > 0 ? Arrays.asList(vpnTrendModel.getTimes().split(",")) : new ArrayList<>();
        //新的时段
        List<String> newTimes = new ArrayList<>();
        //处理时段信息，如果起止时间不是同一个小时，则分解成不同小时,例如0.30-1.29，分解成0.30-0.59，1.00-1.29
        times.forEach(time -> {
            String[] split = time.split("-");
            String startTime = split[0];
            String endTime = split[1];
            String startHour = "", endHour = "", startMinute = "", endMinute = "";
            //如果时段是分钟级，需要使用不同方式
            if (startTime.split("\\.").length > 1) {
                startHour = startTime.split("\\.")[0];
                startMinute = startTime.split("\\.")[1];
            } else {
                startHour = startTime;
                startMinute = "0";
            }
            if (endTime.split("\\.").length > 1) {
                endHour = endTime.split("\\.")[0];
                endMinute = endTime.split("\\.")[1];
            } else {
                endHour = endTime;
                endMinute = "59";
            }
            //如果起止时间不是同一个小时，则分解成不同小时
            if (!startHour.equals(endHour)) {
                //加上原来的首尾时段
                newTimes.add(startHour + "." + startMinute + "-" + startHour + ".59");
                newTimes.add(endHour + ".0-" + endHour + "." + endMinute);
                int start = Integer.parseInt(startHour);
                int end = Integer.parseInt(endHour);
                for (int i = start + 1; i < end; i++) {
                    newTimes.add(i + ".0-" + i + ".59");
                }
            } else {
                newTimes.add(time);
            }
        });
        return newTimes;
    }

    private void blockConditionHandle(VpnTrendModel vpnTrendModel, CommonParamUtil.ParamMap paramMap, StringBuilder stringBuilder) {
        if (BlockFlagEnum.getByCodeEnum(vpnTrendModel.getBlock_flag()) == BlockFlagEnum.Alert) {
            stringBuilder.append(" where block_flag = 1 ").append(" and auth_type = ").append(vpnTrendModel.getArcAccountType());
        } else if (BlockFlagEnum.getByCodeEnum(vpnTrendModel.getBlock_flag()) == BlockFlagEnum.Reject) {
            stringBuilder.append(" where block_flag = 2 ").append(" and auth_type = ").append(vpnTrendModel.getArcAccountType());
        } else if (BlockFlagEnum.getByCodeEnum(vpnTrendModel.getBlock_flag()) == BlockFlagEnum.Log) {
            stringBuilder.append(" where block_flag = 3 ").append(" and auth_type = ").append(vpnTrendModel.getArcAccountType());
        } else {
            stringBuilder.append(" where ").append(" auth_type = ").append(vpnTrendModel.getArcAccountType());
        }
        stringBuilder.append(" and capture_day between ").append("'").append(paramMap.getStartDay()).append("'").append(" and ").append("'").append(paramMap.getEndDay()).append("'");
    }


    @Override
    public Object getProtocolList(Integer type, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        //通过查询DrillDownDataTypeEnum枚举，tableName是否含有NF或者LIS来判断
        DrillDownNormDataTypeEnum[] values = DrillDownNormDataTypeEnum.values();
        for (DrillDownNormDataTypeEnum value : values) {
            if (type == 1 && value.getTableName().contains("dwd_lis")) {
                getDataTypeList(lang, value, result);
            } else if (type == 2 && value.getTableName().contains("dwd_nf") && !value.getValue().contains("vpn")) {
                if (DrillDownNormDataTypeEnum.NF_URL.equals(value) || DrillDownNormDataTypeEnum.NF_OTHER_LOG.equals(value)) {
                    getDataTypeList(lang, value, result);
                }
            } else if (type == 3 && value.getTableName().contains("dwd_vpn") && value.getValue().contains("vpn")) {
                getDataTypeList(lang, value, result);
            }
        }
        return result;
    }

    public void getDataTypeList(String lang, DrillDownNormDataTypeEnum value, List<Map<String, Object>> result) {
        Map<String, Object> map = new HashMap<>();
        map.put("name", arcDrillDownFieldTranslateConfigService.fieldTranslate(CommonConstent.NORM_DATA_TYPE, value.getKey(), lang));
        map.put("dataType", value.getKey());
        result.add(map);
    }

    @Override
    public Object isImportantTarget(Integer arcType, String arcAccount) {
        return basicFeign.isImportantTarget(arcType, arcAccount);
    }

    @Override
    public Object authRecord(String keyword, String type, String virtualAccount, String virtualAppType,
                             String lang, DateModel dateModel, PageWarpEntity pageWarpEntity) {
        // 1 查询关联虚拟账号关联认证账号
        // 2 查询认证对应认证记录
        String redisKey = arcCommonService.getRedisKey("authRecord", keyword, type, virtualAccount, virtualAppType,
                dateModel, pageWarpEntity);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        buildAuthRecordParam(keyword, type, virtualAccount, virtualAppType, paramMap);

        if (StringUtils.isBlank(pageWarpEntity.getSortField())) {
            pageWarpEntity.setSortField("latestTime");
            pageWarpEntity.setSortType(1);
        }

        List<Map<String, Object>> radiusData = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.VIRTUAL_RELATE_AUTH_ACCOUNT.getValue(), paramMap, pageWarpEntity);
        Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.VIRTUAL_RELATE_AUTH_ACCOUNT.getValue(), paramMap);

        if (CollectionUtil.isNotEmpty(radiusData)) {
            for (Map<String, Object> data : radiusData) {
                String accountType = data.get("accountType").toString();
                String account = data.get("account").toString();
                data.put("accountTypeName", arcDrillDownFieldTranslateConfigService.fieldTranslate("auth_type", accountType, lang));

                if (StringUtils.isNotBlank(account) && StringUtils.isNotBlank(accountType)) {
                    String arcId = ArcIdUtil.getAuthAccountId(accountType, account);
                    data.put("arcId", arcId);
                    if (AuthTypeEnum.PHONE.getType().toString().equals(accountType)) {
                        data.put("arcType", ArcTypeEnum.PHONE.getKey());
                    } else if (AuthTypeEnum.FIXED_IP.getType().toString().equals(accountType)) {
                        data.put("arcType", ArcTypeEnum.FIXED_IP.getKey());
                    } else if (AuthTypeEnum.RADIUS.getType().toString().equals(accountType)) {
                        data.put("arcType", ArcTypeEnum.RADIUS.getKey());
                    }
                } else {
                    data.put("arcId", "");
                    data.put("arcType", "");
                }
            }
        }
        HashMap<String, Object> data = new HashMap<>();
        data.put("list", radiusData);
        data.put("total", total);

        redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
        return data;
    }

    private void buildAuthRecordParam(String keyword, String type, String virtualAccount, String virtualAppType, CommonParamUtil.ParamMap paramMap) {
        paramMap.put("virtual_account", virtualAccount);

        if (StringUtils.isNotBlank(virtualAppType)) {
            // TODO 需要逻辑优化 im 档案才考虑拼接
            paramMap.put("app_type_condition", String.format(" and app_type = '%s' ", virtualAppType));
        } else {
            paramMap.put("app_type_condition", "");
        }

        if (StringUtils.isNotBlank(keyword)) {
            paramMap.put("auth_account_condition", String.format(" and auth_account like '%%%s%%'", keyword));
        } else {
            paramMap.put("auth_account_condition", "");
        }

        if (StringUtils.isNotBlank(virtualAppType)) {
            // im
            paramMap.put("data_type", DataTypeEnum.IM.getKey());
        } else {
            // email
            paramMap.put("data_type", DataTypeEnum.EMAIL.getKey());
        }

        if (StringUtils.isNotBlank(type)) {
            if ("0".equals(type)) {
                paramMap.put("auth_type_condition", "");
            } else {
                paramMap.put("auth_type_condition", String.format("and auth_type = '%s'", type));
            }
        } else {
            paramMap.put("auth_type_condition", "");
        }
    }

    @Value("${archive.doris.query.thread:5}")
    private Integer threadNumber;

    @Override
    public Object getDataTypeCount(String arcAccount, DateModel dateModel, String lang) {
//        String redisKey = arcCommonService.getRedisKey("getDataTypeCount", arcAccount, dateModel);
//        Object result = redisOps.get(redisKey);

//        if (result != null && ArcCommonServiceImpl.isOpen) {
//            return ReturnModel.getInstance().ok(result);
//        }

        List<Map<String, Object>> resultList = Collections.synchronizedList(new ArrayList<>());
        ArrayList<String> sqlTemplates = buildSqlTemplates();
        List<List<String>> sqlJobs = Lists.partition(sqlTemplates, threadNumber);

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("phone", arcAccount);

        for (List<String> sqlJob : sqlJobs) {
            try {
                CountDownLatch countDownLatch = new CountDownLatch(sqlJob.size());
                for (String sql : sqlJob) {
                    new ShardJobExecuteThread(composeQueryService, resultList,
                            sql, paramMap, countDownLatch).start();
                }
                countDownLatch.await();
            } catch (Exception e) {
                log.error("sqlJobs execute error", e);
            }
        }

        for (Map<String, Object> metaData : resultList) {
            if (metaData != null) {
                Object dataTypeObj = metaData.get("dataType");
                if (dataTypeObj != null) {
                    String dataType = dataTypeObj.toString();
                    String dataTypeMap = arcDrillDownFieldTranslateConfigService.fieldTranslate(CommonConstent.NORM_DATA_TYPE, dataType, lang).toString();
                    metaData.put("dataTypeMap", dataTypeMap);
                }
            }
        }

        //  通过迭代器 resultList 获取 total, 若 total 为0 则移除
        Iterator<Map<String, Object>> iterator = resultList.iterator();
        while (iterator.hasNext()) {
            Map<String, Object> next = iterator.next();
            String total = next.get("total").toString();
            if ("0".equals(total)) {
                iterator.remove();
            }
        }

//        redisOps.set(redisKey, resultList, DateUtils.getRedisExpireTime());
        return resultList;
    }

    @Override
    public Object getTagTopList(Integer arcType, String arcAccount, String lang) {

        log.info("getTagTopList, arcAccount: {}, arcType: {}", arcAccount, arcType);

        String redisKey = arcCommonService.getRedisKey(arcAccount + arcType, "arcTagTop", arcAccount, arcType);
        if (ArcCommonServiceImpl.isOpen) {
            Object redisResult = redisOps.get(redisKey);
            if (redisResult != null) {
                return ReturnModel.getInstance().ok(redisResult);
            }
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(new DateModel(null, null, tagTopDateOpt));
        paramMap.put("arc_name", arcAccount);
        paramMap.put("arc_type", arcType);
        paramMap.put("top_num", tagTopDateNum);

        //查询档案特定的模板语句
        List<Map<String, Object>> tagData = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.COMMON_GET_TAG_TOP_LIST.getValue(), paramMap);

        HashMap<String, Object> data = new HashMap<>();

        //对tagName去重加国际化
        postProcessTagNameList(tagData, lang);

        data.put("topTags", tagData);

        if (ArcCommonServiceImpl.isOpen) {
            redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
        }

        return data;
    }

    @Override
    public Object getTagList(Integer arcType, String arcAccount, String keyword, DateModel dateModel, PageWarpEntity pageWarpEntity, String lang) {

        String redisKey = arcCommonService.getRedisKey(arcAccount + arcType, "arcTagList", arcAccount, arcType, keyword,
                dateModel, pageWarpEntity);
        if (ArcCommonServiceImpl.isOpen) {

            Object result = redisOps.get(redisKey);
            if (result != null) {
                return ReturnModel.getInstance().ok(result);
            }
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildTagListParam(dateModel);
        paramMap.put("arc_name", arcAccount);
        paramMap.put("arc_type", arcType);

        if (StringUtils.isBlank(pageWarpEntity.getSortField())) {
            pageWarpEntity.setSortField("behaviorNum");
            pageWarpEntity.setSortType(1);
        }

        if (StringUtils.isBlank(keyword)) {
            paramMap.put("keyword_condition", "");
        } else {
            String allTagName = (String) arcDrillDownFieldTranslateConfigService.getAllTagNameTranslate(lang);
            ArrayList<String> matchTagName = Lists.newArrayList();
            if (StringUtils.isNotEmpty(allTagName)) {
                String[] allTagNameArr = allTagName.split(",");
                for (String tagNameKeyTranslate : allTagNameArr) {
                    if (tagNameKeyTranslate.contains(keyword)) {
                        matchTagName.add(EscapeUtil.escapeSingleQuote(tagNameKeyTranslate.substring(0, tagNameKeyTranslate.indexOf(":"))));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(matchTagName)) {

                paramMap.put("keyword_condition", " and (tag_name in (" + StringTemplateUtil.appendComma(matchTagName, true) + ") or (lower(tag_value) like '%" + EscapeUtil.escapeSingleQuote(keyword).toLowerCase() + "%' or tag_value like '%" + EscapeUtil.escapeSingleQuote(keyword) + "%')) ");
            } else {
                paramMap.put("keyword_condition", " and  (lower(tag_value) like '%" + EscapeUtil.escapeSingleQuote(keyword).toLowerCase() + "%'  or tag_value like '%" + EscapeUtil.escapeSingleQuote(keyword) + "%') ");
            }
        }

        List<Map<String, Object>> tagData = arcCommonService.getCommonServiceListResult(paramMap.containsKey("start_day") ? BusinessCodeEnum.COMMON_GET_TAG_LIST_DAY.getValue()
                : BusinessCodeEnum.COMMON_GET_TAG_LIST_MONTH.getValue(), paramMap, pageWarpEntity);
        Long total = arcCommonService.getCommonServiceCountResult(paramMap.containsKey("start_day") ? BusinessCodeEnum.COMMON_GET_TAG_LIST_DAY.getValue()
                : BusinessCodeEnum.COMMON_GET_TAG_LIST_MONTH.getValue(), paramMap);

        HashMap<String, Object> data = new HashMap<>();

        //对tagName去重加国际化
        postProcessTagNameList(tagData, lang);

        data.put("tagList", tagData);
        data.put("total", total);
        if (ArcCommonServiceImpl.isOpen) {
            redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
        }
        return data;
    }

    private void postProcessTagNameList(List<Map<String, Object>> tagData, String lang) {
        if (CollectionUtils.isEmpty(tagData)) {
            return;
        }
        //去重
        tagData.forEach(tagMap -> {
            if (StringUtils.isNotEmpty(tagMap.getOrDefault("tagName", "").toString()) && tagMap.get("tagName").toString().contains(",")) {
                tagMap.put("tagName", ArcCommonUtils.removeDuplicatesPreserveOrder(tagMap.get("tagName").toString(), ","));
            }
        });
        //国际化
        tagData.forEach(tagMap -> {
            if (StringUtils.isNotEmpty(tagMap.getOrDefault("tagName", "").toString())) {
                tagMap.put("tagName", arcDrillDownFieldTranslateConfigService.tagNameTranslate(tagMap.get("tagName").toString(), lang));
            }
        });
    }

    static class ShardJobExecuteThread extends Thread {

        ComposeQueryService composeQueryService;

        List<Map<String, Object>> resultList;

        String sql;

        CommonParamUtil.ParamMap paramMap;

        CountDownLatch countDownLatch;

        public ShardJobExecuteThread(ComposeQueryService composeQueryService, List<Map<String, Object>> resultList,
                                     String sql, CommonParamUtil.ParamMap paramMap, CountDownLatch countDownLatch) {
            this.composeQueryService = composeQueryService;
            this.resultList = resultList;
            this.sql = sql;
            this.paramMap = paramMap;
            this.countDownLatch = countDownLatch;
        }

        @Override
        public void run() {
            try {
                List<Map<String, Object>> maps = composeQueryService.queryForList(sql, paramMap, "GET_DATA_TYPE_COUNT");
                if (CollectionUtil.isNotEmpty(maps)) {
                    resultList.addAll(maps);
                }
            } catch (Throwable e) {
                log.error("execute sql error, the sql is \r\n {} \r\n", sql, e);
            } finally {
                countDownLatch.countDown();
            }
        }
    }

    private ArrayList<String> buildSqlTemplates() {
        ArrayList<String> sqlTemplates = new ArrayList<>();

        sqlTemplates.add("SELECT count() total, '100' dataType FROM ${dwd}.dwd_lis_http \n" +
                "where auth_account = '${phone}' and auth_type = 1020004 and capture_day >= {start_day} and capture_day <= {end_day}\n" +
                "and earliest_relation_time >= ${start_time} and latest_relation_time  <= ${end_time}");

        sqlTemplates.add("SELECT count() total, '101' dataType FROM ${dwd}.dwd_lis_email \n" +
                "where auth_account = '${phone}' and auth_type = 1020004 and capture_day >= {start_day} and capture_day <= {end_day}\n" +
                "and capture_time >= ${start_time} and capture_time <= ${end_time}");

        sqlTemplates.add("SELECT count() total, '103' dataType FROM ${dwd}.dwd_lis_im \n" +
                "where auth_account = '${phone}' and auth_type = 1020004 and capture_day >= {start_day} and capture_day <= {end_day}\n" +
                "and capture_time >= ${start_time} and capture_time <= ${end_time}");

        sqlTemplates.add("SELECT count() total, '121' dataType FROM ${dwd}.dwd_lis_vpn\n" +
                "where auth_account = '${phone}' and auth_type = 1020004 and capture_day >= {start_day} and capture_day <= {end_day}\n" +
                "and capture_time >= ${start_time} and capture_time <= ${end_time}");
//
//        sqlTemplates.add("SELECT count() total, '2109' dataType FROM ${dwd}.dwd_lis_voip\n" +
//                "where auth_account = '${phone}' and auth_type = 1020004 and capture_day >= {start_day} and capture_day <= {end_day}\n" +
//                "and capture_time >= ${start_time} and capture_time <= ${end_time}");

        sqlTemplates.add("SELECT count() total, '105' dataType FROM ${dwd}.dwd_lis_ftp\n" +
                "where auth_account = '${phone}' and auth_type = 1020004 and capture_day >= {start_day} and capture_day >= {end_day}\n" +
                "and capture_time >= ${start_time} and capture_time <= ${end_time}");

        sqlTemplates.add("SELECT count() total, '949' dataType FROM ${dwd}.dwd_lis_tool\n" +
                "where auth_account = '${phone}' and auth_type = 1020004 and capture_day >= {start_day} and capture_day <= {end_day}\n" +
                "and capture_time >= ${start_time} and capture_time <= ${end_time}");

        sqlTemplates.add("SELECT count() total, '113' dataType FROM ${dwd}.dwd_lis_remote \n" +
                "where auth_account = '${phone}' and auth_type = 1020004 and capture_day >= {start_day} and capture_day <= {end_day}\n" +
                "and capture_time >= ${start_time} and capture_time <= ${end_time}");

        sqlTemplates.add("SELECT count() total, norm_data_type dataType FROM ${dwd}.dwd_lis_others\n" +
                "where auth_account = '${phone}' and auth_type = 1020004 and capture_day >= {start_day} and capture_day <= {end_day}\n" +
                "and capture_time >= ${start_time} and capture_time <= ${end_time}\n" +
                "group by norm_data_type ");

        return sqlTemplates;
    }

    @Override
    public boolean saveCustomColumns(CustomColumnsModel columns, String userId) {
        QueryWrapper<ArcCustomColumnsEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("data_type", columns.getDataType());
        String jsonString = JSON.toJSONString(columns.getColumns());
        JSONArray jsonArray = JSONArray.parseArray(jsonString);

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String sortable = jsonObject.getString("sortable");

            if (sortable.equals("true")) {
                jsonObject.put("sortable", true);
            } else if (sortable.equals("false")){
                jsonObject.put("sortable", false);
            }
        }

        ArcCustomColumnsEntity oldEntity = customColumnsService.getOne(wrapper);

        if (oldEntity != null) {
            oldEntity.setColumns(jsonArray.toJSONString());
            oldEntity.setModifyTime(System.currentTimeMillis());
            customColumnsService.update(oldEntity, wrapper);
        } else {
            ArcCustomColumnsEntity entity = new ArcCustomColumnsEntity();
            entity.setUserId(userId);
            entity.setDataType(columns.getDataType());
            entity.setColumns(jsonArray.toJSONString());
            entity.setCreateTime(System.currentTimeMillis());
            entity.setModifyTime(System.currentTimeMillis());
            customColumnsService.save(entity);
        }

        return true;
    }
}