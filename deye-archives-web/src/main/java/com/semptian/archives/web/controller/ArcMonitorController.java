package com.semptian.archives.web.controller;

import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.core.common.enums.SortFieldEnum;
import com.semptian.archives.web.interceptor.ArcPermissionCheck;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.common.util.PermissionUtil;
import com.semptian.archives.web.service.model.ArcQueryModel;
import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.service.ArchivesInfoService;
import com.semptian.base.service.CustomException;
import com.semptian.base.service.ReturnModel;
import com.semptian.base.util.JsonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description data archives controller used to support operation and maintenance monitoring api
 * <AUTHOR>
 * @Date 2025/7/24 16:08
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/monitor")
@Slf4j
@Api
public class ArcMonitorController {

    @Resource
    private ArchivesInfoService arcInfoService;

    @Value("${limit.total:10000}")
    private Integer limitTotal;

    @Value("${archive.communityDigAppId.appId:119}")
    private String communityDigAppId;

    @GetMapping("/arc_info.json")
    @ResponseBody
    @ApiOperation(value = "query archives info", notes = "Retrieve archived system data for monitoring and analysis purposes", httpMethod = "GET", response = ReturnModel.class)
    @ArcPermissionCheck
    public Object queryArcInfo(ArcQueryModel arcQueryModel, DateModel dateModel, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        String appId = request.getHeader("appId");
        //如果是群组挖掘调用该接口赋予全息所有档案权限
        List<Integer> permissionList;
        if (communityDigAppId.equals(appId)) {
            permissionList = Arrays.asList(1, 2, 3, 4, 5, 6, 7);
        } else {
            permissionList = PermissionUtil.getArcPermission(request);
        }
        log.debug("request /arc_info/arc_info.json parameter:{}", JsonUtil.toJsonString(arcQueryModel));

        if (StringUtils.isBlank(userId)) {
            log.warn("request /arc_info/arc_info.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        //校验isCare
        Integer isCare = arcQueryModel.getIsCare();
        if (isCare == null || !(isCare == 0 || isCare == 1)) {
            throw new CustomException(ArcServerReturnCode.PARAM_IS_INVALID.getCode(), I18nUtils.getMessage("isCare must be 0 or 1."));
        }

        //限制size
        if (arcQueryModel.getSize() > 100) {
            throw new CustomException(ArcServerReturnCode.PARAM_IS_INVALID.getCode(), I18nUtils.getMessage("size can not be too lager."));
        }
        //校验权限
        if (arcQueryModel.getArcType() != 0 && !permissionList.contains(arcQueryModel.getArcType())) {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("arc.permission.error"));
        }

        Integer onPage = arcQueryModel.getOnPage();
        Integer size = arcQueryModel.getSize();
        if (onPage != null) {
            if (onPage * size > limitTotal) {
                onPage = limitTotal / size;
                arcQueryModel.setOnPage(onPage);
            }
        }
        arcQueryModel.setUserId(userId);

        if(arcQueryModel.getSortType()==null){
            arcQueryModel.setSortType(1);
        }

        //默认排序字段为活跃次数
        if (StringUtils.isBlank(arcQueryModel.getSortField())) {
            arcQueryModel.setSortField(SortFieldEnum.ACTIVE_NUM.getField());
        }

        // 过滤掉逻辑组合查询
        if (StringUtils.isNotBlank(arcQueryModel.getKeyWord())) {
            String keyword = arcQueryModel.getKeyWord().replaceAll("(?i)( OR | NOT )", " ");
            arcQueryModel.setKeyWord(keyword);
        }

        return arcInfoService.queryArcInfo(arcQueryModel, dateModel, permissionList);
    }
}
