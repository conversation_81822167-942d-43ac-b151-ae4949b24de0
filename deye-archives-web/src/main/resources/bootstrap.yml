spring:
  profiles:
    active: dev
  application:
    name: deye-archives-web-hwj

  cloud:
    nacos:
      #Nacos服务注册中心地址
      discovery:
        # 开发环境
        server-addr: 192.168.80.63:8848
        # 测试环境
#        server-addr: 192.168.80.96:8848
        namespace: semptian
      #Nacos作为配置中心地址
      config:
        prefix: deye-archives-service
        # 开发环境
        server-addr: 192.168.80.63:8848
        # 测试环境
#        server-addr: 192.168.80.96:8848
        file-extension: yaml
        namespace: semptian
        group: DEFAULT_GROUP
        shared-configs:
          - data-id: common-redis.yaml
            group: DEFAULT_GROUP
          - data-id: common-dat-gateway.yaml
            group: DEFAULT_GROUP